﻿using LibBusinessModules.DB.Models.Device;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.PC
{
    /// <summary>
    /// 平行样测试数据
    ///</summary>
    [SugarTable("doublecheck_data")]
    [Description("平行样测试数据")]
    public class DoubleCheckData
    {
        #region 字段属性

        /// <summary>
        /// 设备序列号
        ///</summary>
        [SugarColumn(ColumnName = "sncode", IsPrimaryKey = true)]
        [Description("设备序列号")]
        public string SNCode { get; set; }

        /// <summary>
        /// 数据时间
        ///</summary>
        [SugarColumn(ColumnName = "time", IsPrimaryKey = true, SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "数据时间")]
        [Description("数据时间")]
        public DateTime Time { get; set; }

        /// <summary>
        /// 第一次测量值
        ///</summary>
        [SugarColumn(ColumnName = "value1", ColumnDescription = "第一次测量值")]
        [Description("第一次测量值")]
        public double Value1 { get; set; }

        /// <summary>
        /// 第二次测量值
        ///</summary>
        [SugarColumn(ColumnName = "value2", ColumnDescription = "第二次测量值")]
        [Description("第二次测量值")]
        public double Value2 { get; set; }

        /// <summary>
        /// 第一次测量时间
        ///</summary>
        [SugarColumn(ColumnName = "time1", ColumnDescription = "第一次测量时间")]
        [Description("第一次测量时间")]
        public DateTime Time1 { get; set; }

        /// <summary>
        /// 第二次测量时间
        ///</summary>
        [SugarColumn(ColumnName = "time2", ColumnDescription = "第二次测量时间")]
        [Description("第二次测量时间")]
        public DateTime Time2 { get; set; }

        /// <summary>
        /// 误差
        ///</summary>
        [SugarColumn(ColumnName = "err", ColumnDescription = "误差")]
        [Description("误差")]
        public double Err { get; set; }

        /// <summary>
        /// 误差判定
        ///</summary>
        [SugarColumn(ColumnName = "judge", ColumnDescription = "误差判定")]
        [Description("误差判定")]
        public string Judge { get; set; }

        #endregion

        #region 公共方法

        /// <summary>
        /// 从设备数据库生成数据
        /// </summary>
        public static List<DoubleCheckData> InitFromDeviceDB(SqlSugarClient db, string snCode)
        {
            try
            {
                var dataList = new List<DoubleCheckData>();

                List<RawDoubleCheckData> rawDoubleCheckDataList = db.Queryable<RawDoubleCheckData>().ToList();
                foreach(var rawDoubleCheckData in rawDoubleCheckDataList)
                {
                    try
                    {
                        DoubleCheckData doubleCheckData = new DoubleCheckData();
                        doubleCheckData.SNCode = snCode;
                        doubleCheckData.Time = DateTime.Parse(rawDoubleCheckData.Time);
                        doubleCheckData.Value1 = double.Parse(rawDoubleCheckData.Value1);
                        doubleCheckData.Value2 = double.Parse(rawDoubleCheckData.Value2);
                        doubleCheckData.Time1 = DateTime.Parse(rawDoubleCheckData.Time1);
                        doubleCheckData.Time2 = DateTime.Parse(rawDoubleCheckData.Time2);
                        doubleCheckData.Err = double.Parse(rawDoubleCheckData.Err);
                        doubleCheckData.Judge = rawDoubleCheckData.Judge;

                        dataList.Add(doubleCheckData);
                    }
                    catch
                    {
                    }
                }

                return dataList;
            }
            catch(Exception ex)
            {
                throw new Exception($"从数据库提取平行样测试数据出错：{ex.Message}");
            }
        }

        #endregion
    }
}