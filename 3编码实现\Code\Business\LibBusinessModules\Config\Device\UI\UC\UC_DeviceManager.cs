﻿using Sunny.UI;
using System;
using System.Windows.Forms;

namespace LibBusinessModules.Config.UI
{
    public partial class UC_DeviceManager : UIUserControl
    {
        #region 构造

        public UC_DeviceManager()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void UC_DeviceManager_Load(object sender, EventArgs e)
        {
            dgvDeviceList.Columns.Clear();

            dgvDeviceList.Columns.Add("Number", "序号");
            dgvDeviceList.Columns.Add("Factor", "因子类型");
            dgvDeviceList.Columns.Add("Model", "设备型号");
            dgvDeviceList.Columns.Add("IdCode", "ID识别码");
            dgvDeviceList.Columns.Add("InspectionBasis", "检验依据");
            dgvDeviceList.Columns.Add("StandardRange", "标准量程");
            dgvDeviceList.Columns.Add("StandardRangeUnit", "标准量程单位");
            dgvDeviceList.Columns.Add("AuxiliaryRange", "辅助量程");
            dgvDeviceList.Columns.Add("AuxiliaryRangeUnit", "辅助量程");

            foreach(DataGridViewColumn column in dgvDeviceList.Columns)
            {
                column.SortMode = DataGridViewColumnSortMode.NotSortable;
            }

            RefreshUI();
        }

        #region 按钮编辑

        private void btnAdd_Click(object sender, EventArgs e)
        {
            AddDeviceConfig();
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            EditDeviceConfig();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            DeleteDeviceConfig();
        }

        #endregion

        #region 右键菜单编辑

        private void tsmAdd_Click(object sender, EventArgs e)
        {
            AddDeviceConfig();
        }

        private void tsmEdit_Click(object sender, EventArgs e)
        {
            EditDeviceConfig();
        }

        private void tsmDelete_Click(object sender, EventArgs e)
        {
            DeleteDeviceConfig();
        }

        #endregion

        #region 双击控件

        private void dgvDeviceList_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            EditDeviceConfig();
        }

        #endregion

        /// <summary>
        /// 保存修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSave_Click(object sender, EventArgs e)
        {
            DeviceManager.GetInstance().Save();
            UIMessageBox.ShowSuccess("保存成功！");
        }

        /// <summary>
        /// 重置修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            DeviceManager.GetInstance().ReLoad();
            RefreshUI();
            UIMessageBox.ShowSuccess("重置修改成功！");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新数据
        /// </summary>
        public void RefreshUI()
        {
            dgvDeviceList.Rows.Clear();
            int number = 1;
            foreach(DeviceConfig deviceConfig in DeviceManager.GetInstance().GetDeviceList())
            {
                int rowIndex = dgvDeviceList.AddRow();
                DataGridViewRow dr = dgvDeviceList.Rows[rowIndex];
                dr.Cells["Number"].Value = number;
                dr.Cells["Factor"].Value = deviceConfig.Factor;
                dr.Cells["Model"].Value = deviceConfig.Model;
                dr.Cells["IdCode"].Value = deviceConfig.IdCode;
                dr.Cells["InspectionBasis"].Value = deviceConfig.InspectionBasis;
                dr.Cells["StandardRange"].Value = deviceConfig.StandardRange;
                dr.Cells["StandardRangeUnit"].Value = deviceConfig.StandardRangeUnit;
                dr.Cells["AuxiliaryRange"].Value = deviceConfig.AuxiliaryRange;
                dr.Cells["AuxiliaryRangeUnit"].Value = deviceConfig.AuxiliaryRangeUnit;

                dr.Tag = deviceConfig;

                number++;
            }
        }

        #region 增删改

        private void AddDeviceConfig()
        {
            try
            {
                DeviceConfig deviceConfig = new DeviceConfig();
                deviceConfig.InitMeasureItems();
                if(new FrmDeviceConfig(deviceConfig).ShowDialog() == DialogResult.OK)
                {
                    DeviceManager.GetInstance().DeviceList.Add(deviceConfig);
                    RefreshUI();
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"添加失败：{ex.Message}");
            }
        }

        private void EditDeviceConfig()
        {
            try
            {
                if(dgvDeviceList.SelectedRows.Count < 1)
                {
                    throw new Exception("请先选中待编辑因子！");
                }
                if(dgvDeviceList.SelectedRows[0].Tag is DeviceConfig deviceConfig)
                {
                    if(new FrmDeviceConfig(deviceConfig, true).ShowDialog() == DialogResult.OK)
                    {
                        RefreshUI();
                    }
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"编辑失败：{ex.Message}");
            }
        }

        private void DeleteDeviceConfig()
        {
            try
            {
                if(dgvDeviceList.SelectedRows.Count < 1)
                {
                    throw new Exception("请先选中待编辑因子！");
                }
                if(dgvDeviceList.SelectedRows[0].Tag is DeviceConfig deviceConfig)
                {
                    if(UIMessageBox.ShowAsk($"确认删除因子[{deviceConfig.Factor}]配置信息？"))
                    {
                        DeviceManager.GetInstance().DeviceList.Remove(deviceConfig);
                        RefreshUI();
                    }
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"删除失败：{ex.Message}");
            }
        }

        #endregion

        #endregion
    }
}