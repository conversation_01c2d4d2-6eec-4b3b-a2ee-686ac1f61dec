﻿using LibBaseModules.DB;
using LibBusinessModules.DB.Models.Device;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.PC
{
    /// <summary>
    /// 高指测量数据
    ///</summary>
    [SugarTable("imn_measure_data")]
    [Description("高指测量数据")]
    public class ImnMeasureData : BaseNode
    {
        #region 字段属性

        /// <summary>
        /// 设备序列号
        ///</summary>
        [SugarColumn(ColumnName = "sncode", IsPrimaryKey = true)]
        [Description("设备序列号")]
        public string SNCode { get; set; }

        /// <summary>
        /// 时间
        ///</summary>
        [SugarColumn(ColumnName = "time", IsPrimaryKey = true, SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "数据时间")]
        [Description("数据时间")]
        public DateTime Time { get; set; }

        /// <summary>
        /// 测量值
        ///</summary>
        [SugarColumn(ColumnName = "value", ColumnDescription = "浓度")]
        [Description("测量值")]
        public double Value { get; set; }

        /// <summary>
        /// 量程
        ///</summary>
        [SugarColumn(ColumnName = "range", ColumnDescription = "量程")]
        [Description("量程")]
        public double Range { get; set; }

        /// <summary>
        /// 数据标识
        ///</summary>
        [SugarColumn(ColumnName = "flag", ColumnDescription = "数据标识")]
        [Description("数据标识")]
        public string Flag { get; set; }

        /// <summary>
        /// 数据类型
        ///</summary>
        [SugarColumn(ColumnName = "type", ColumnDescription = "数据类型")]
        [Description("数据类型")]
        public string Type { get; set; }

        /// <summary>
        /// 修正滴定数
        ///</summary>
        [SugarColumn(ColumnName = "titration_amend", ColumnDescription = "修正滴定数")]
        [Description("修正滴定数")]
        public double TitrationAmend { get; set; }

        /// <summary>
        /// 滴定数
        ///</summary>
        [SugarColumn(ColumnName = "titration", ColumnDescription = "滴定数")]
        [Description("滴定数")]
        public double Titration { get; set; }

        /// <summary>
        /// 吸光度1
        ///</summary>
        [SugarColumn(ColumnName = "abs1", ColumnDescription = "吸光度1")]
        [Description("吸光度1")]
        public double Abs1 { get; set; }

        /// <summary>
        /// 检测信号1
        ///</summary>
        [SugarColumn(ColumnName = "signalMain1", ColumnDescription = "检测信号1")]
        [Description("检测信号1")]
        public double SignalMain1 { get; set; }

        /// <summary>
        /// 参比信号1
        ///</summary>
        [SugarColumn(ColumnName = "signalRef1", ColumnDescription = "参比信号1")]
        [Description("参比信号1")]
        public double SignalRef1 { get; set; }

        /// <summary>
        /// 吸光度2
        ///</summary>
        [SugarColumn(ColumnName = "abs2", ColumnDescription = "吸光度2")]
        [Description("吸光度2")]
        public double Abs2 { get; set; }

        /// <summary>
        /// 检测信号2
        ///</summary>
        [SugarColumn(ColumnName = "signalMain2", ColumnDescription = "检测信号2")]
        [Description("检测信号2")]
        public double SignalMain2 { get; set; }

        /// <summary>
        /// 参比信号2
        ///</summary>
        [SugarColumn(ColumnName = "signalRef2", ColumnDescription = "参比信号2")]
        [Description("参比信号2")]
        public double SignalRef2 { get; set; }

        /// <summary>
        /// 测量类型
        ///</summary>
        [SugarColumn(ColumnName = "type1", ColumnDescription = "测量类型")]
        [Description("测量类型")]
        public string Type1 { get; set; }

        #endregion

        #region 公共方法

        /// <summary>
        /// 从设备数据库生成数据
        /// </summary>
        public static List<ImnMeasureData> InitFromDeviceDB(SqlSugarClient db, string snCode)
        {
            try
            {
                List<ImnMeasureData> dataList = new List<ImnMeasureData>();

                List<ImnRawMeasuredata> imnRawMeasureDataList = db.Queryable<ImnRawMeasuredata>().ToList();
                foreach(ImnRawMeasuredata imnRawMeasureData in imnRawMeasureDataList)
                {
                    try
                    {
                        ImnMeasureData curveData = new ImnMeasureData();
                        curveData.SNCode = snCode;
                        curveData.Time = DateTime.Parse(imnRawMeasureData.Time);
                        curveData.Value = double.Parse(imnRawMeasureData.Value);
                        curveData.Range = double.Parse(imnRawMeasureData.Range);
                        curveData.Flag = imnRawMeasureData.Flag;
                        curveData.Type1 = imnRawMeasureData.Type1;
                        curveData.Type = imnRawMeasureData.Type;
                        curveData.TitrationAmend = double.Parse(imnRawMeasureData.TitrationAmend);
                        curveData.Titration = double.Parse(imnRawMeasureData.Titration);
                        curveData.Abs1 = double.Parse(imnRawMeasureData.Abs1);
                        curveData.SignalMain1 = double.Parse(imnRawMeasureData.SignalMain1);
                        curveData.SignalRef1 = double.Parse(imnRawMeasureData.SignalRef1);
                        curveData.Abs2 = double.Parse(imnRawMeasureData.Abs2);
                        curveData.SignalMain2 = double.Parse(imnRawMeasureData.SignalMain2);
                        curveData.SignalRef2 = double.Parse(imnRawMeasureData.SignalRef2);

                        dataList.Add(curveData);
                    }
                    catch
                    {
                    }
                }

                return dataList;
            }
            catch(Exception ex)
            {
                throw new Exception($"从数据库提取高指测量数据出错：{ex.Message}");
            }
        }

        #endregion
    }
}