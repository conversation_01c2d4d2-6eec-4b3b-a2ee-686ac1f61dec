﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 柱塞泵软件版本
    ///</summary>
    [SugarTable("pump_version_info")]
    [Description("柱塞泵软件版本")]
    public class PumpVersionInfo
    {
        /// <summary>
        /// 软件版本
        ///</summary>
        [SugarColumn(ColumnName = "version")]
        [Description("软件版本")]
        public string? Version { get; set; }
    }
}