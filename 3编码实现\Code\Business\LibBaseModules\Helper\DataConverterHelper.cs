﻿using System;
using System.Globalization;
using System.Text;

namespace LibBaseModules.Helper
{
    /// <summary>
    /// 数据转换帮助类
    /// </summary>
    public static class DataConverterHelper
    {
        /// <summary>
        /// 解析时间
        /// </summary>
        /// <param name="timeStr"></param>
        /// <returns></returns>
        public static DateTime? ParseDateTime(string? timeStr)
        {
            return DateTime.TryParseExact(timeStr, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime time) ? time : null;
        }

        /// <summary>
        /// 解析反序浮点数
        /// </summary>
        /// <param name="value"></param>
        /// <param name="startIndex"></param>
        /// <returns></returns>
        public static float ToSingle(byte[] value, int startIndex)
        {
            return BitConverter.ToSingle(ReverseByte(value, startIndex, 4), 0);
        }

        /// <summary>
        /// 解析反序短整形
        /// </summary>
        /// <param name="value"></param>
        /// <param name="startIndex"></param>
        /// <returns></returns>
        public static ushort ToInt16(byte[] value, int startIndex)
        {
            return BitConverter.ToUInt16(ReverseByte(value, startIndex, 2), 0);
        }

        /// <summary>
        /// 解析反序短整形
        /// </summary>
        /// <param name="value"></param>
        /// <param name="startIndex"></param>
        /// <returns></returns>
        public static int ToInt32(byte[] value, int startIndex)
        {
            return BitConverter.ToInt32(ReverseByte(value, startIndex, 4), 0);
        }

        /// <summary>
        /// 反转字符串
        /// </summary>
        /// <param name="value"></param>
        /// <param name="startIndex"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public static byte[] ReverseByte(byte[] value, int startIndex, int length)
        {
            byte[] b = new byte[length];
            for(int i = 0; i < b.Length; i++)
            {
                b[length - 1 - i] = value[startIndex + i];
            }
            return b;
        }

        /// <summary>
        /// byte数组转字符串
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public static string BytesToString(byte[] data)
        {
            if(data == null || data.Length == 0)
            {
                return string.Empty;
            }
            StringBuilder sb = new StringBuilder();
            for(int i = 0; i < data.Length; i++)
            {
                string s = ByteToString(data[i]);
                sb.Append(s).Append(" ");
            }
            return sb.ToString();
        }

        /// <summary>
        /// byte转字符串
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string ByteToString(byte value)
        {
            return value.ToString("X2");
        }

        /// <summary>
        /// BCD码转为10进制
        /// 例：0x40对应40
        /// </summary>
        /// <param name="data"></param>
        public static int ConvertBcdToInt(int data)
        {
            return int.Parse(data.ToString("x2"));
        }
    }
}