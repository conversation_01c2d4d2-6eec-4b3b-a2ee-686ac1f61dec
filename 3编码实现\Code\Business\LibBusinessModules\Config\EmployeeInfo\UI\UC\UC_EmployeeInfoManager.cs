﻿using Sunny.UI;
using System;
using System.Windows.Forms;

namespace LibBusinessModules.Config.UI
{
    public partial class UC_EmployeeInfoManager : UIUserControl
    {
        #region 构造

        public UC_EmployeeInfoManager()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void UC_EmployeeInfoManager_Load(object sender, EventArgs e)
        {
            dgvEmployeeList.Columns.Clear();
            dgvEmployeeList.Columns.Add("Number", "序号");
            dgvEmployeeList.Columns.Add("ID", "工号");
            dgvEmployeeList.Columns.Add("Name", "姓名");
            dgvEmployeeList.Columns.Add("Position", "岗位");

            foreach(DataGridViewColumn column in dgvEmployeeList.Columns)
            {
                column.SortMode = DataGridViewColumnSortMode.NotSortable;
            }

            RefreshUI();
        }

        #region 按钮编辑

        private void btnAdd_Click(object sender, EventArgs e)
        {
            AddDeviceConfig();
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            EditDeviceConfig();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            DeleteDeviceConfig();
        }

        #endregion

        #region 右键菜单编辑

        private void tsmAdd_Click(object sender, EventArgs e)
        {
            AddDeviceConfig();
        }

        private void tsmEdit_Click(object sender, EventArgs e)
        {
            EditDeviceConfig();
        }

        private void tsmDelete_Click(object sender, EventArgs e)
        {
            DeleteDeviceConfig();
        }

        #endregion

        #region 双击控件

        private void dgvEmployeeList_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            EditDeviceConfig();
        }

        #endregion

        /// <summary>
        /// 保存修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSave_Click(object sender, EventArgs e)
        {
            SystemConfig.GetInstance().Save();
            UIMessageBox.ShowSuccess("保存成功！");
        }

        /// <summary>
        /// 重置修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            SystemConfig.GetInstance().ReLoad();
            RefreshUI();
            UIMessageBox.ShowSuccess("重置修改成功！");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新数据
        /// </summary>
        public void RefreshUI()
        {
            dgvEmployeeList.Rows.Clear();
            int number = 1;
            foreach(EmployeeInfo employeeInfo in SystemConfig.GetInstance().GetAllEmployeeInfo())
            {
                int rowIndex = dgvEmployeeList.AddRow();
                DataGridViewRow dr = dgvEmployeeList.Rows[rowIndex];
                dr.Cells["Number"].Value = number;
                dr.Cells["ID"].Value = employeeInfo.ID;
                dr.Cells["Name"].Value = employeeInfo.Name;
                dr.Cells["Position"].Value = employeeInfo.Position;

                dr.Tag = employeeInfo;

                number++;
            }
        }

        #region 增删改

        private void AddDeviceConfig()
        {
            try
            {
                EmployeeInfo employeeInfo = new EmployeeInfo();
                if(new FrmEmployeeInfoConfig(employeeInfo).ShowDialog() == DialogResult.OK)
                {
                    SystemConfig.GetInstance().EmployeeList.Add(employeeInfo);
                    RefreshUI();
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"添加失败：{ex.Message}");
            }
        }

        private void EditDeviceConfig()
        {
            try
            {
                if(dgvEmployeeList.SelectedRows.Count < 1)
                {
                    throw new Exception("请先选中待编辑因子！");
                }
                if(dgvEmployeeList.SelectedRows[0].Tag is EmployeeInfo employeeInfo)
                {
                    if(new FrmEmployeeInfoConfig(employeeInfo, true).ShowDialog() == DialogResult.OK)
                    {
                        RefreshUI();
                    }
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"编辑失败：{ex.Message}");
            }
        }

        private void DeleteDeviceConfig()
        {
            try
            {
                if(dgvEmployeeList.SelectedRows.Count < 1)
                {
                    throw new Exception("请先选中待编辑因子！");
                }
                if(dgvEmployeeList.SelectedRows[0].Tag is EmployeeInfo employeeInfo)
                {
                    if(UIMessageBox.ShowAsk($"确认删除员工[{employeeInfo.Name}]信息？"))
                    {
                        SystemConfig.GetInstance().EmployeeList.Remove(employeeInfo);
                        RefreshUI();
                    }
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"删除失败：{ex.Message}");
            }
        }

        #endregion

        #endregion
    }
}