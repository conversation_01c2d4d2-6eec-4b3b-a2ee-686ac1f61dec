﻿using LibBusinessModules.UI;
using System;
using System.IO;
using System.Windows.Forms;

namespace IAutoTestX
{
    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new FrmMain());
            }
            catch(Exception e)
            {
                RecordErrorInfo(e);
            }
            finally
            {
                // 释放资源
                GC.Collect();
                GC.WaitForPendingFinalizers();
                Environment.Exit(0);
            }
        }

        #region 异常信息记录

        /// <summary>
        /// 异常信息记录
        /// </summary>
        /// <param name="exception"></param>
        private static void RecordErrorInfo(Exception exception)
        {
            if(exception == null) return;
            string filename = AppDomain.CurrentDomain.BaseDirectory + @"\error-" + DateTime.Now.ToString("yyyyMMdd") + ".txt";
            FileStream fileStream = null;
            try
            {
                fileStream = new FileStream(filename, FileMode.Append, FileAccess.Write, FileShare.Read);
                using(var streamWriter = new StreamWriter(fileStream))
                {
                    if(exception.Message != null)
                    {
                        streamWriter.WriteLine("[{0:yyyy-MM-dd HH:mm:ss}]:错误信息：{1}", DateTime.Now, exception.Message);
                    }
                    if(exception.StackTrace != null)
                    {
                        streamWriter.WriteLine("[{0:yyyy-MM-dd HH:mm:ss}]:调用堆栈：{1}", DateTime.Now,
                            exception.StackTrace);
                    }
                    if(exception.TargetSite != null)
                    {
                        streamWriter.WriteLine("[{0:yyyy-MM-dd HH:mm:ss}]:方法名称：{1}", DateTime.Now, exception.TargetSite.Name);
                    }
                    streamWriter.WriteLine(string.Empty);
                }
            }
            finally
            {
                fileStream?.Close();
            }
        }

        #endregion
    }
}