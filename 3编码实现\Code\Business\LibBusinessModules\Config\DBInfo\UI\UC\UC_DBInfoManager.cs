﻿using SqlSugar;
using Sunny.UI;
using System;

namespace LibBusinessModules.Config.UI
{
    public partial class UC_DBInfoManager : UIUserControl
    {
        #region 构造

        public UC_DBInfoManager()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void UC_DBInfoManager_Load(object sender, EventArgs e)
        {
            RefreshUI();
        }

        private void rdbSqlite_CheckedChanged(object sender, EventArgs e)
        {
            pnlMysqlConfig.Enabled = !rdbSqlite.Checked;
        }

        /// <summary>
        /// 保存修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if(string.IsNullOrEmpty(txtDatabaseName.Text))
                {
                    throw new Exception("数据库名不可为空！");
                }

                if(rdbMySql.Checked)
                {
                    if(string.IsNullOrEmpty(txtServerIP.Text))
                    {
                        throw new Exception("数据库IP不可为空！");
                    }
                    if(txtServerPort.Value == 0)
                    {
                        throw new Exception("数据库端口不可为空！");
                    }
                    if(string.IsNullOrEmpty(txtUserName.Text))
                    {
                        throw new Exception("数据库用户名不可为空！");
                    }
                    if(string.IsNullOrEmpty(txtPassword.Text))
                    {
                        throw new Exception("数据库密码不可为空！");
                    }
                }

                SystemConfig.GetInstance().DBInfo.DatebaseType = rdbSqlite.Checked ? DbType.Sqlite : DbType.MySql;
                SystemConfig.GetInstance().DBInfo.DatabaseName = txtDatabaseName.Text;
                if(rdbMySql.Checked)
                {
                    SystemConfig.GetInstance().DBInfo.ServerIP = txtServerIP.Text;
                    SystemConfig.GetInstance().DBInfo.ServerPort = txtServerPort.Value;
                    SystemConfig.GetInstance().DBInfo.UserName = txtUserName.Text;
                    SystemConfig.GetInstance().DBInfo.Password = txtPassword.Text;
                }
                SystemConfig.GetInstance().Save();

                UIMessageBox.ShowSuccess("保存成功！");
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"保存出错：{ex.Message}");
            }
        }

        /// <summary>
        /// 重置修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            SystemConfig.GetInstance().ReLoad();
            RefreshUI();
            UIMessageBox.ShowSuccess("重置修改成功！");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新数据
        /// </summary>
        public void RefreshUI()
        {
            rdbSqlite.Checked = SystemConfig.GetInstance().DBInfo.DatebaseType == DbType.Sqlite;
            rdbMySql.Checked = SystemConfig.GetInstance().DBInfo.DatebaseType == DbType.MySql;

            txtDatabaseName.Text = SystemConfig.GetInstance().DBInfo.DatabaseName;
            txtServerIP.Text = SystemConfig.GetInstance().DBInfo.ServerIP;
            txtServerPort.Value = SystemConfig.GetInstance().DBInfo.ServerPort;
            txtUserName.Text = SystemConfig.GetInstance().DBInfo.UserName;
            txtPassword.Text = SystemConfig.GetInstance().DBInfo.Password;
        }

        #endregion
    }
}