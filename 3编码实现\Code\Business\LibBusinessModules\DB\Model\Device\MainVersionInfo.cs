﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 主板软件版本
    ///</summary>
    [SugarTable("main_version_info")]
    [Description("主板软件版本")]
    public class MainVersionInfo
    {
        /// <summary>
        /// 软件版本
        ///</summary>
        [SugarColumn(ColumnName = "version")]
        [Description("软件版本")]
        public string? Version { get; set; }
    }
}