﻿using LibBaseModules.Helper;
using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using Sunny.UI;
using System.Windows.Forms;

namespace LibBusinessModules.UI.DataQuery
{
    /// <summary>
    /// 高指校准数据查询
    /// </summary>
    public partial class UC_ImnCalibrationDataQuery : UC_DataQueryBase
    {
        public UC_ImnCalibrationDataQuery()
        {
            InitializeComponent();
            QueryDataName = "高指校准数据";
        }

        protected override void SetViewHead()
        {
            dgvRecords.Columns.Clear();
            dgvRecords.Columns.Add("Num", "序号");
            dgvRecords.Columns.Add("SNCode", "设备序列号");
            dgvRecords.Columns.Add("Time", "数据时间");
            dgvRecords.Columns.Add("Value", "测量值");
            dgvRecords.Columns.Add("Range", "量程");
            dgvRecords.Columns.Add("Flag", "数据标识");
            dgvRecords.Columns.Add("Type1", "类型1");
            dgvRecords.Columns.Add("Type2", "类型2");
            dgvRecords.Columns.Add("TitrationAmend", "修正滴定数");
            dgvRecords.Columns.Add("Titration", "滴定数");
            dgvRecords.Columns.Add("Abs1", "吸光度1");
            dgvRecords.Columns.Add("SignalMain1", "检测信号1");
            dgvRecords.Columns.Add("SignalRef1", "参比信号1");
            dgvRecords.Columns.Add("Abs2", "吸光度2");
            dgvRecords.Columns.Add("SignalMain2", "检测信号2");
            dgvRecords.Columns.Add("SignalRef2", "参比信号2");
        }

        protected override int QueryDataCount()
        {
            var query = DBHelper.GetPCDBContext().Queryable<ImnCalibrationData>()
                           .Where(data => data.Time >= StartTime && data.Time <= EndTime);

            if(!string.IsNullOrEmpty(SnCode))
            {
                query = query.Where(data => data.SNCode.Contains(SnCode));
            }

            return query.Count();
        }

        protected override void FillDataToDgv()
        {
            try
            {
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据查询中，请稍候...");

                // 查询当前页数据
                var query = DBHelper.GetPCDBContext().Queryable<ImnCalibrationData>()
                               .Where(data => data.Time >= StartTime && data.Time <= EndTime);
                if(!string.IsNullOrEmpty(SnCode))
                {
                    query = query.Where(data => data.SNCode.Contains(SnCode));
                }
                var dataList = query.ToPageList(CurPage - 1, PageSize);

                UIFormServiceHelper.HideWaitForm(this.ParentForm);

                UIFormServiceHelper.ShowStatusForm(this.ParentForm, dataList.Count, "数据渲染中，请稍候...");
                //ExtensionsMethod
                int index = 1;
                foreach(var data in dataList)
                {
                    int rowIndex = dgvRecords.AddRow();
                    DataGridViewRow dr = dgvRecords.Rows[rowIndex];
                    dr.Cells["Num"].Value = index;
                    dr.Cells["SNCode"].Value = data.SNCode;
                    dr.Cells["Time"].Value = data.Time.ToDisplayFormat();
                    dr.Cells["Value"].Value = data.Value.ToString("F4");
                    dr.Cells["Range"].Value = data.Range;
                    dr.Cells["Flag"].Value = data.Flag;
                    dr.Cells["Type1"].Value = data.Type1;
                    dr.Cells["Type2"].Value = data.Type;
                    dr.Cells["TitrationAmend"].Value = data.TitrationAmend.ToString("F4");
                    dr.Cells["Titration"].Value = data.Titration.ToString("F4");
                    dr.Cells["Abs1"].Value = data.Abs1.ToString("F4");
                    dr.Cells["SignalMain1"].Value = data.SignalMain1.ToString("F4");
                    dr.Cells["SignalRef1"].Value = data.SignalRef1.ToString("F4");
                    dr.Cells["Abs2"].Value = data.Abs2.ToString("F4");
                    dr.Cells["SignalMain2"].Value = data.SignalMain2.ToString("F4");
                    dr.Cells["SignalRef2"].Value = data.SignalRef2.ToString("F4");

                    dr.Tag = data;

                    UIFormServiceHelper.SetStatusFormDescription(this.ParentForm, $"数据渲染中[{index++}/{dataList.Count}]......");
                    UIFormServiceHelper.SetStatusFormStepIt(this.ParentForm, index);
                    // 线程切换，防止最终进度界面无法关闭
                    //Thread.Sleep(1);
                }
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                //Thread.Sleep(100);
                // 隐藏进度条界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
                UIFormServiceHelper.HideStatusForm(this.ParentForm);
            }
        }
    }
}