﻿using System;
using System.IO;
using System.Windows.Forms;

namespace JsonDecryptTool
{
    internal static class Program
    {
        [STAThread]
        private static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            var directoryInfo = new DirectoryInfo(Application.StartupPath);
            FileInfo[] files = directoryInfo.GetFiles("*.json");
            foreach(FileInfo fileInfo in files)
            {
                try
                {
                    DesFile(fileInfo.FullName);
                }
                catch(Exception)
                {
                    // ignored
                }
            }
            MessageBox.Show(@"解密完成!", @"提示");
        }

        private static void DesFile(string fileName)
        {
            // 读取文件内容
            string fileContent = File.ReadAllText(fileName);
            // 解密
            fileContent = AesCryptHelper.Decrypt(fileContent);
            // 覆写
            if(File.Exists(fileName))
            {
                File.SetAttributes(fileName, FileAttributes.Normal);
                File.Delete(fileName);
            }
            using(TextWriter textWriter = File.CreateText(fileName))
            {
                textWriter.Write(fileContent);
                textWriter.Flush();
            }
        }
    }
}