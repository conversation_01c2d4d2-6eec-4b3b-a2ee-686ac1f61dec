﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 屏幕软件版本
    ///</summary>
    [SugarTable("hmi_version_info")]
    [Description("屏幕软件版本")]
    public class HmiVersionInfo
    {
        /// <summary>
        /// 软件版本
        ///</summary>
        [SugarColumn(ColumnName = "version")]
        [Description("软件版本")]
        public string? Version { get; set; }
    }
}