﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 曲线数据
    ///</summary>
    [SugarTable("lineData")]
    [Description("曲线数据")]
    public class LineData
    {
        /// <summary>
        /// 时间
        ///</summary>
        [SugarColumn(ColumnName = "time", IsNullable = true)]
        [Description("时间")]
        public string? Time { get; set; }

        /// <summary>
        /// 量程
        ///</summary>
        [SugarColumn(ColumnName = "range")]
        [Description("量程")]
        public string? Range { get; set; }

        /// <summary>
        /// 截距
        ///</summary>
        [SugarColumn(ColumnName = "A0")]
        [Description("截距")]
        public string? A0 { get; set; }

        /// <summary>
        /// 斜率
        ///</summary>
        [SugarColumn(ColumnName = "A1")]
        [Description("斜率")]
        public string? A1 { get; set; }

        /// <summary>
        /// 二次项
        ///</summary>
        [SugarColumn(ColumnName = "A2")]
        [Description("二次项")]
        public string? A2 { get; set; }

        /// <summary>
        /// 标1关联校准点时间
        ///</summary>
        [SugarColumn(ColumnName = "timeAdj1")]
        [Description("标1关联校准点时间")]
        public string? TimeAdj1 { get; set; }

        /// <summary>
        /// 标2关联校准点时间
        ///</summary>
        [SugarColumn(ColumnName = "timeAdj2")]
        [Description("标2关联校准点时间")]
        public string? TimeAdj2 { get; set; }

        /// <summary>
        /// 中间点关联校准点时间
        ///</summary>
        [SugarColumn(ColumnName = "timeAdjM")]
        [Description("中间点关联校准点时间")]
        public string? TimeAdjM { get; set; }
    }
}