﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB
{
    /// <summary>
    /// 故障分类表
    ///</summary>
    [SugarTable("alarmdata")]
    [Description("故障分类表")]
    public class FaultCategory
    {
        #region 字段属性

        /// <summary>
        /// 类型编号
        ///</summary>
        [Description("类型编号")]
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int CategoryID { get; set; }

        /// <summary>
        /// 类型名称
        ///</summary>
        [Description("类型名称")]
        [SugarColumn(Length = 50, IsNullable = false)]
        public string CategoryName { get; set; }

        /// <summary>
        /// 父类型编号,为null表示是一级类型
        /// </summary>
        [Description("父类型编号")]
        [SugarColumn(IsNullable = true)]
        public int? ParentCategoryID { get; set; }

        /// <summary>
        /// 类型级别
        /// </summary>
        [Description("类型级别")]
        [SugarColumn(IsNullable = false)]
        public int CategoryLevel { get; set; }

        /// <summary>
        /// 故障描述
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = true)]
        public string Description { get; set; }

        #endregion
    }
}