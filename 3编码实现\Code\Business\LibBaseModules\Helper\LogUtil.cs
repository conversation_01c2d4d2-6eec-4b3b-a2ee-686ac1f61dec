﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading;

namespace LibBaseModules.Helper
{
    /// <summary>
    /// 日志模块
    /// </summary>
    public class LogUtil : IDisposable
    {
        #region 字段

        /// <summary>
        /// 日志写入线程的控制标记  ture写中|false没有写
        /// </summary>
        private bool _state = true;

        /// <summary>
        /// 日志对象的缓存队列
        /// </summary>
        private readonly Queue<Msg> _msgs = new Queue<Msg>();

        /// <summary>
        /// 日志文件写入流对象
        /// </summary>
        private StreamWriter _writer;

        /// <summary>
        /// 日志文件默认目录
        /// </summary>
        private readonly string _logDirectory = AppDomain.CurrentDomain.BaseDirectory + @"Log\";

        /// <summary>
        /// 输出日志事件
        /// </summary>
        public Action<Msg> OnLogOutEvent;

        /// <summary>
        /// 文件时间
        /// </summary>
        private DateTime _fileTime;

        #endregion

        #region 单例

        /// <summary>
        /// 创建日志对象的新实例,根据指定的日志文件路径和指定的日志文件创建类型
        /// </summary>
        private LogUtil()
        {
            new Thread(obj =>
            {
                try
                {
                    while(_state)
                    {
                        //判断队列中是否存在待写入的日志
                        if(_msgs.Count > 0)
                        {
                            lock(_msgs)
                            {
                                Msg msg = _msgs.Dequeue();
                                if(msg != null)
                                {
                                    FileWrite(msg);
                                    //LogOutEvent?.Invoke(msg);
                                    OnLogOutEvent?.BeginInvoke(msg, null, null);
                                }
                            }
                        }
                        else
                        {
                            //判断是否已经发出终止日志并关闭的消息
                            if(_state)
                            {
                                Thread.Sleep(100);
                            }
                            else
                            {
                                break;
                            }
                        }
                    }
                }
                finally
                {
                    FileClose();
                }
            })
            {
                IsBackground = true,
                Name = "日志处理线程"
            }.Start();
        }

        private static LogUtil _instance;
        private static readonly object synObject = new object();

        /// <summary>
        /// 单例
        /// </summary>
        public static LogUtil GetInstance()
        {
            if(null == _instance)
            {
                lock(synObject)
                {
                    if(null == _instance)
                    {
                        _instance = new LogUtil();
                    }
                }
            }
            return _instance;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 写入新日志,根据指定的日志内容和信息类型,采用当前时间为日志时间写入新日志
        /// </summary>
        /// <param name="text">日志内容</param>
        /// <param name="type">信息类型</param>
        public void LogWrite(string text, MsgLevel type = MsgLevel.Trace)
        {
            var msg = new Msg(DateTime.Now, text, type);
            LogWrite(msg);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 写入新日志,根据指定的日志时间、日志内容和信息类型写入新日志
        /// </summary>
        /// <param name="dt">日志时间</param>
        /// <param name="text">日志内容</param>
        /// <param name="type">信息类型</param>
        public void LogWrite(DateTime dt, string text, MsgLevel type = MsgLevel.Trace)
        {
            var msg = new Msg(dt, text, type);
            LogWrite(msg);
        }

        /// <summary>
        /// 写入日志文本到文件的方法
        /// </summary>
        /// <param name="msg"></param>
        private void FileWrite(Msg msg)
        {
            try
            {
                if(_writer == null)
                {
                    FileOpen();
                    _fileTime = DateTime.Now;
                }
                // 日期变更  更换日志文件
                else if(msg.Datetime.Day != _fileTime.Day)
                {
                    FileClose();
                    FileOpen();
                    _fileTime = msg.Datetime;
                }

                if(_writer != null)
                {
                    _writer.WriteLine(msg.ToString());
                    _writer.Flush();
                }
            }
            catch(Exception e)
            {
                //Console.Out.Write(e);
            }
        }

        /// <summary>
        /// 打开文件准备写入
        /// </summary>
        private void FileOpen()
        {
            if(!Directory.Exists(_logDirectory))
            {
                Directory.CreateDirectory(_logDirectory);
            }
            _writer = new StreamWriter(_logDirectory + DateTime.Now.ToString("yyyy-MM-dd") + ".txt", true, Encoding.UTF8);
        }

        /// <summary>
        /// 关闭打开的日志文件
        /// </summary>
        private void FileClose()
        {
            if(_writer != null)
            {
                _writer.Flush();
                _writer.Close();
                _writer.Dispose();
                _writer = null;
            }
        }

        /// <summary>
        /// 写入新日志,根据指定的日志对象Msg
        /// </summary>
        /// <param name="msg">日志内容对象</param>
        private void LogWrite(Msg msg)
        {
            if(_msgs != null)
            {
                lock(_msgs)
                {
                    _msgs.Enqueue(msg);
                }
            }
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// 销毁日志对象
        /// </summary>
        public void Dispose()
        {
            _state = false;
        }

        #endregion
    }

    /// <summary>
    /// 一个日志记录的对象
    /// </summary>
    public class Msg
    {
        #region 字段属性

        /// <summary>
        /// 日志记录的时间
        /// </summary>
        public DateTime Datetime { get; set; }

        /// <summary>
        ///日志记录的内容
        /// </summary>
        public string Text { get; set; }

        /// <summary>
        /// 日志等级
        /// </summary>
        public MsgLevel Type { get; set; }

        #endregion

        #region 构造

        /// <summary>
        /// 创建新的日志记录实例;
        /// </summary>
        /// <param name="dt">日志记录的时间</param>
        /// <param name="t">日志记录的文本内容</param>
        /// <param name="p">日志记录的消息类型</param>
        public Msg(DateTime dt, string t, MsgLevel p)
        {
            Datetime = dt;
            Type = p;
            Text = t;
        }

        /// <summary>
        /// 创建新的日志记录实例;
        /// </summary>
        /// <param name="t">日志记录的文本内容</param>
        /// <param name="p">日志记录的消息类型</param>
        public Msg(string t, MsgLevel p = MsgLevel.Info)
        {
            Datetime = DateTime.Now;
            Type = p;
            Text = t;
        }

        #endregion

        #region 方法重写

        public override string ToString()
        {
            return $"[{Datetime:yyyy-MM-dd HH:mm:ss fff}][{Type}]:{Text}";
        }

        #endregion
    }

    /// <summary>
    /// 日志等级类型 Trace=0 Info Warn Error
    /// </summary>
    public enum MsgLevel
    {
        /// <summary>
        /// 通信信息
        /// </summary>
        Trace = 0,

        /// <summary>
        /// 普通信息
        /// </summary>
        Info,

        /// <summary>
        /// 错误信息
        /// </summary>
        Error
    }
}