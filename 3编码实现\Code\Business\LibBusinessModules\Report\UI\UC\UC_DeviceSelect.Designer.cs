﻿namespace LibBusinessModules.Report.UI
{
    partial class UC_DeviceSelect
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.gbDeviceSelect = new Sunny.UI.UIGroupBox();
            this.cmbDeviceList = new Sunny.UI.UIComboDataGridView();
            this.uiLabel1 = new Sunny.UI.UILabel();
            this.gbDeviceSelect.SuspendLayout();
            this.SuspendLayout();
            // 
            // gbDeviceSelect
            // 
            this.gbDeviceSelect.Controls.Add(this.cmbDeviceList);
            this.gbDeviceSelect.Controls.Add(this.uiLabel1);
            this.gbDeviceSelect.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbDeviceSelect.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbDeviceSelect.Location = new System.Drawing.Point(1, 1);
            this.gbDeviceSelect.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbDeviceSelect.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbDeviceSelect.Name = "gbDeviceSelect";
            this.gbDeviceSelect.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.gbDeviceSelect.Size = new System.Drawing.Size(395, 107);
            this.gbDeviceSelect.TabIndex = 63;
            this.gbDeviceSelect.Text = "设备选择";
            this.gbDeviceSelect.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // cmbDeviceList
            // 
            this.cmbDeviceList.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbDeviceList.FillColor = System.Drawing.Color.White;
            this.cmbDeviceList.Font = new System.Drawing.Font("宋体", 12F);
            this.cmbDeviceList.Location = new System.Drawing.Point(58, 29);
            this.cmbDeviceList.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbDeviceList.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbDeviceList.Name = "cmbDeviceList";
            this.cmbDeviceList.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbDeviceList.Size = new System.Drawing.Size(330, 29);
            this.cmbDeviceList.SymbolSize = 24;
            this.cmbDeviceList.TabIndex = 75;
            this.cmbDeviceList.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbDeviceList.Watermark = "";
            this.cmbDeviceList.ValueChanged += new Sunny.UI.UIComboDataGridView.OnValueChanged(this.cmbDeviceList_ValueChanged);
            // 
            // uiLabel1
            // 
            this.uiLabel1.AutoSize = true;
            this.uiLabel1.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel1.Font = new System.Drawing.Font("宋体", 12F);
            this.uiLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel1.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.uiLabel1.Location = new System.Drawing.Point(12, 35);
            this.uiLabel1.Name = "uiLabel1";
            this.uiLabel1.Size = new System.Drawing.Size(39, 16);
            this.uiLabel1.Style = Sunny.UI.UIStyle.Custom;
            this.uiLabel1.TabIndex = 40;
            this.uiLabel1.Text = "SN码";
            this.uiLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // UC_DeviceSelect
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.gbDeviceSelect);
            this.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "UC_DeviceSelect";
            this.Padding = new System.Windows.Forms.Padding(1);
            this.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.Size = new System.Drawing.Size(397, 109);
            this.Text = "设备选择";
            this.Load += new System.EventHandler(this.UC_DeviceSelect_Load);
            this.Enter += new System.EventHandler(this.UC_DeviceSelect_Enter);
            this.gbDeviceSelect.ResumeLayout(false);
            this.gbDeviceSelect.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
        private Sunny.UI.UIGroupBox gbDeviceSelect;
        private Sunny.UI.UILabel uiLabel1;
        private Sunny.UI.UIComboDataGridView cmbDeviceList;
    }
}
