﻿namespace LibBusinessModules.UI.DataQuery
{
     partial class UC_DataQueryBase
    {
        /// <summary> 
        /// 必需的设计器变量。

        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。

        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。

        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(UC_DataQueryBase));
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            this.pnlTitle = new Sunny.UI.UIPanel();
            this.txtSnCode = new Sunny.UI.UITextBox();
            this.uiLabel1 = new Sunny.UI.UILabel();
            this.btnEnd = new Sunny.UI.UIButton();
            this.btnFirst = new Sunny.UI.UIButton();
            this.btnNext = new Sunny.UI.UIButton();
            this.btnPrev = new Sunny.UI.UIButton();
            this.txtRecordCount = new Sunny.UI.UITextBox();
            this.lblPage = new Sunny.UI.UILabel();
            this.uiLabel7 = new Sunny.UI.UILabel();
            this.uiLabel6 = new Sunny.UI.UILabel();
            this.btnExcelExport = new Sunny.UI.UIButton();
            this.dtpEndTime = new Sunny.UI.UIDatetimePicker();
            this.dtpStartTime = new Sunny.UI.UIDatetimePicker();
            this.btnStartQuery = new Sunny.UI.UIButton();
            this.uiLabel2 = new Sunny.UI.UILabel();
            this.uiLabel3 = new Sunny.UI.UILabel();
            this.saveFileDialog = new System.Windows.Forms.SaveFileDialog();
            this.dgvRecords = new Sunny.UI.UIDataGridView();
            this.pnlTitle.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvRecords)).BeginInit();
            this.SuspendLayout();
            // 
            // pnlTitle
            // 
            this.pnlTitle.Controls.Add(this.txtSnCode);
            this.pnlTitle.Controls.Add(this.uiLabel1);
            this.pnlTitle.Controls.Add(this.btnEnd);
            this.pnlTitle.Controls.Add(this.btnFirst);
            this.pnlTitle.Controls.Add(this.btnNext);
            this.pnlTitle.Controls.Add(this.btnPrev);
            this.pnlTitle.Controls.Add(this.txtRecordCount);
            this.pnlTitle.Controls.Add(this.lblPage);
            this.pnlTitle.Controls.Add(this.uiLabel7);
            this.pnlTitle.Controls.Add(this.uiLabel6);
            this.pnlTitle.Controls.Add(this.btnExcelExport);
            this.pnlTitle.Controls.Add(this.dtpEndTime);
            this.pnlTitle.Controls.Add(this.dtpStartTime);
            this.pnlTitle.Controls.Add(this.btnStartQuery);
            this.pnlTitle.Controls.Add(this.uiLabel2);
            this.pnlTitle.Controls.Add(this.uiLabel3);
            resources.ApplyResources(this.pnlTitle, "pnlTitle");
            this.pnlTitle.FillColor = System.Drawing.Color.White;
            this.pnlTitle.Name = "pnlTitle";
            this.pnlTitle.Style = Sunny.UI.UIStyle.Custom;
            this.pnlTitle.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // txtSnCode
            // 
            this.txtSnCode.Cursor = System.Windows.Forms.Cursors.IBeam;
            resources.ApplyResources(this.txtSnCode, "txtSnCode");
            this.txtSnCode.Name = "txtSnCode";
            this.txtSnCode.ShowText = false;
            this.txtSnCode.Style = Sunny.UI.UIStyle.Custom;
            this.txtSnCode.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtSnCode.Watermark = "";
            // 
            // uiLabel1
            // 
            resources.ApplyResources(this.uiLabel1, "uiLabel1");
            this.uiLabel1.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel1.Name = "uiLabel1";
            this.uiLabel1.Style = Sunny.UI.UIStyle.Custom;
            // 
            // btnEnd
            // 
            this.btnEnd.Cursor = System.Windows.Forms.Cursors.Hand;
            resources.ApplyResources(this.btnEnd, "btnEnd");
            this.btnEnd.Name = "btnEnd";
            this.btnEnd.Style = Sunny.UI.UIStyle.Custom;
            this.btnEnd.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnEnd.Click += new System.EventHandler(this.btnLast_Click);
            // 
            // btnFirst
            // 
            this.btnFirst.Cursor = System.Windows.Forms.Cursors.Hand;
            resources.ApplyResources(this.btnFirst, "btnFirst");
            this.btnFirst.Name = "btnFirst";
            this.btnFirst.Style = Sunny.UI.UIStyle.Custom;
            this.btnFirst.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnFirst.Click += new System.EventHandler(this.btnFirst_Click);
            // 
            // btnNext
            // 
            this.btnNext.Cursor = System.Windows.Forms.Cursors.Hand;
            resources.ApplyResources(this.btnNext, "btnNext");
            this.btnNext.Name = "btnNext";
            this.btnNext.Style = Sunny.UI.UIStyle.Custom;
            this.btnNext.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // btnPrev
            // 
            this.btnPrev.Cursor = System.Windows.Forms.Cursors.Hand;
            resources.ApplyResources(this.btnPrev, "btnPrev");
            this.btnPrev.Name = "btnPrev";
            this.btnPrev.Style = Sunny.UI.UIStyle.Custom;
            this.btnPrev.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnPrev.Click += new System.EventHandler(this.btnPrev_Click);
            // 
            // txtRecordCount
            // 
            this.txtRecordCount.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtRecordCount.DoubleValue = 600D;
            resources.ApplyResources(this.txtRecordCount, "txtRecordCount");
            this.txtRecordCount.IntValue = 600;
            this.txtRecordCount.Name = "txtRecordCount";
            this.txtRecordCount.ShowText = false;
            this.txtRecordCount.Style = Sunny.UI.UIStyle.Custom;
            this.txtRecordCount.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtRecordCount.Type = Sunny.UI.UITextBox.UIEditType.Integer;
            this.txtRecordCount.Watermark = "";
            // 
            // lblPage
            // 
            resources.ApplyResources(this.lblPage, "lblPage");
            this.lblPage.BackColor = System.Drawing.Color.Transparent;
            this.lblPage.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblPage.Name = "lblPage";
            this.lblPage.Style = Sunny.UI.UIStyle.Custom;
            // 
            // uiLabel7
            // 
            resources.ApplyResources(this.uiLabel7, "uiLabel7");
            this.uiLabel7.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel7.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel7.Name = "uiLabel7";
            this.uiLabel7.Style = Sunny.UI.UIStyle.Custom;
            // 
            // uiLabel6
            // 
            resources.ApplyResources(this.uiLabel6, "uiLabel6");
            this.uiLabel6.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel6.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel6.Name = "uiLabel6";
            this.uiLabel6.Style = Sunny.UI.UIStyle.Custom;
            // 
            // btnExcelExport
            // 
            resources.ApplyResources(this.btnExcelExport, "btnExcelExport");
            this.btnExcelExport.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnExcelExport.Name = "btnExcelExport";
            this.btnExcelExport.Style = Sunny.UI.UIStyle.Custom;
            this.btnExcelExport.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnExcelExport.Click += new System.EventHandler(this.btnExcelExport_Click);
            // 
            // dtpEndTime
            // 
            this.dtpEndTime.FillColor = System.Drawing.Color.White;
            resources.ApplyResources(this.dtpEndTime, "dtpEndTime");
            this.dtpEndTime.MaxLength = 19;
            this.dtpEndTime.Name = "dtpEndTime";
            this.dtpEndTime.Style = Sunny.UI.UIStyle.Custom;
            this.dtpEndTime.SymbolDropDown = 61555;
            this.dtpEndTime.SymbolNormal = 61555;
            this.dtpEndTime.SymbolSize = 24;
            this.dtpEndTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.dtpEndTime.Value = new System.DateTime(2021, 1, 25, 10, 0, 3, 579);
            this.dtpEndTime.Watermark = "";
            // 
            // dtpStartTime
            // 
            this.dtpStartTime.FillColor = System.Drawing.Color.White;
            resources.ApplyResources(this.dtpStartTime, "dtpStartTime");
            this.dtpStartTime.MaxLength = 19;
            this.dtpStartTime.Name = "dtpStartTime";
            this.dtpStartTime.Style = Sunny.UI.UIStyle.Custom;
            this.dtpStartTime.SymbolDropDown = 61555;
            this.dtpStartTime.SymbolNormal = 61555;
            this.dtpStartTime.SymbolSize = 24;
            this.dtpStartTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.dtpStartTime.Value = new System.DateTime(2021, 1, 25, 10, 0, 3, 579);
            this.dtpStartTime.Watermark = "";
            // 
            // btnStartQuery
            // 
            resources.ApplyResources(this.btnStartQuery, "btnStartQuery");
            this.btnStartQuery.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnStartQuery.Name = "btnStartQuery";
            this.btnStartQuery.Style = Sunny.UI.UIStyle.Custom;
            this.btnStartQuery.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnStartQuery.Click += new System.EventHandler(this.btnStartQuery_Click);
            // 
            // uiLabel2
            // 
            resources.ApplyResources(this.uiLabel2, "uiLabel2");
            this.uiLabel2.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel2.Name = "uiLabel2";
            this.uiLabel2.Style = Sunny.UI.UIStyle.Custom;
            // 
            // uiLabel3
            // 
            resources.ApplyResources(this.uiLabel3, "uiLabel3");
            this.uiLabel3.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel3.Name = "uiLabel3";
            this.uiLabel3.Style = Sunny.UI.UIStyle.Custom;
            // 
            // saveFileDialog
            // 
            resources.ApplyResources(this.saveFileDialog, "saveFileDialog");
            this.saveFileDialog.OverwritePrompt = false;
            // 
            // dgvRecords
            // 
            this.dgvRecords.AllowUserToAddRows = false;
            this.dgvRecords.AllowUserToDeleteRows = false;
            this.dgvRecords.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.dgvRecords.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            this.dgvRecords.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvRecords.AutoSizeRowsMode = System.Windows.Forms.DataGridViewAutoSizeRowsMode.AllCells;
            this.dgvRecords.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.dgvRecords.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle2.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle2.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvRecords.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            resources.ApplyResources(this.dgvRecords, "dgvRecords");
            this.dgvRecords.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(236)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvRecords.DefaultCellStyle = dataGridViewCellStyle3;
            this.dgvRecords.EnableHeadersVisualStyles = false;
            this.dgvRecords.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(173)))), ((int)(((byte)(255)))));
            this.dgvRecords.Name = "dgvRecords";
            this.dgvRecords.ReadOnly = true;
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvRecords.RowHeadersDefaultCellStyle = dataGridViewCellStyle4;
            this.dgvRecords.RowHeadersVisible = false;
            dataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle5.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle5.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(236)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle5.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.dgvRecords.RowsDefaultCellStyle = dataGridViewCellStyle5;
            this.dgvRecords.RowTemplate.Height = 29;
            this.dgvRecords.SelectedIndex = -1;
            this.dgvRecords.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            // 
            // UC_DataQueryBase
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.dgvRecords);
            this.Controls.Add(this.pnlTitle);
            resources.ApplyResources(this, "$this");
            this.Name = "UC_DataQueryBase";
            this.Load += new System.EventHandler(this.QueryDataUC_Load);
            this.pnlTitle.ResumeLayout(false);
            this.pnlTitle.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvRecords)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
        private Sunny.UI.UIButton btnEnd;
        private Sunny.UI.UIButton btnFirst;
        private Sunny.UI.UIButton btnNext;
        private Sunny.UI.UIButton btnPrev;
        private Sunny.UI.UITextBox txtRecordCount;
        private Sunny.UI.UILabel lblPage;
        private Sunny.UI.UILabel uiLabel7;
        private Sunny.UI.UILabel uiLabel6;
        private Sunny.UI.UIButton btnExcelExport;
        private Sunny.UI.UIDatetimePicker dtpEndTime;
        private Sunny.UI.UIDatetimePicker dtpStartTime;
        private Sunny.UI.UIButton btnStartQuery;
        private Sunny.UI.UILabel uiLabel2;
        private Sunny.UI.UILabel uiLabel3;
        protected Sunny.UI.UIPanel pnlTitle;
        private System.Windows.Forms.SaveFileDialog saveFileDialog;
        private Sunny.UI.UITextBox txtSnCode;
        private Sunny.UI.UILabel uiLabel1;
        protected Sunny.UI.UIDataGridView dgvRecords;
    }
}
