﻿using LibBaseModules.DB;
using LibBaseModules.Helper;
using LibBusinessModules.DB.Models.Device;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.PC
{
    /// <summary>
    /// 曲线数据
    ///</summary>
    [SugarTable("curve_data")]
    [Description("曲线数据")]
    public class CurveData : BaseNode
    {
        #region 字段属性

        /// <summary>
        /// 设备序列号
        ///</summary>
        [SugarColumn(ColumnName = "sncode", IsPrimaryKey = true)]
        [Description("设备序列号")]
        public string SNCode { get; set; }

        /// <summary>
        /// 数据时间
        ///</summary>
        [SugarColumn(ColumnName = "time", IsPrimaryKey = true, SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "数据时间")]
        [Description("数据时间")]
        public DateTime Time { get; set; }

        /// <summary>
        /// 量程
        ///</summary>
        [SugarColumn(ColumnName = "range", ColumnDescription = "量程")]
        [Description("量程")]
        public double Range { get; set; }

        /// <summary>
        /// 斜率
        ///</summary>
        [SugarColumn(ColumnName = "A0", ColumnDescription = "斜率")]
        [Description("斜率")]
        public double K { get; set; }

        /// <summary>
        /// 截距
        ///</summary>
        [SugarColumn(ColumnName = "A1", ColumnDescription = "截距")]
        [Description("截距")]
        public double B { get; set; }

        /// <summary>
        /// 二次项
        ///</summary>
        [SugarColumn(ColumnName = "A2", ColumnDescription = "二次项")]
        [Description("二次项")]
        public double A2 { get; set; }

        /// <summary>
        /// 标1关联校准点时间
        ///</summary>
        [SugarColumn(ColumnName = "timeAdj1", IsNullable = true, SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "标1关联校准点时间")]
        [Description("标1关联校准点时间")]
        public DateTime? TimeAdj1 { get; set; }

        /// <summary>
        /// 标2关联校准点时间
        ///</summary>
        [SugarColumn(ColumnName = "timeAdj2", IsNullable = true, SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "标2关联校准点时间")]
        [Description("标2关联校准点时间")]
        public DateTime? TimeAdj2 { get; set; }

        /// <summary>
        /// 中间点关联校准点时间
        ///</summary>
        [SugarColumn(ColumnName = "timeAdjM", IsNullable = true, SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "中间点关联校准点时间")]
        [Description("中间点关联校准点时间")]
        public DateTime? TimeAdjM { get; set; }

        #endregion

        #region 公共方法

        /// <summary>
        /// 从设备数据库生成数据
        /// </summary>
        public static List<CurveData> InitFromDeviceDB(SqlSugarClient db, string snCode)
        {
            try
            {
                var dataList = new List<CurveData>();

                List<LineData> lineDataList = db.Queryable<LineData>().ToList();
                foreach(var lineData in lineDataList)
                {
                    try
                    {
                        CurveData curveData = new CurveData();
                        curveData.SNCode = snCode;
                        curveData.Time = DateTime.Parse(lineData.Time);
                        curveData.Range = double.Parse(lineData.Range);
                        curveData.B = double.Parse(lineData.A0);
                        curveData.K = double.Parse(lineData.A1);
                        curveData.A2 = double.Parse(lineData.A2);
                        curveData.TimeAdj1 = DataConverterHelper.ParseDateTime(lineData.TimeAdj1);
                        curveData.TimeAdj2 = DataConverterHelper.ParseDateTime(lineData.TimeAdj2);
                        curveData.TimeAdjM = DataConverterHelper.ParseDateTime(lineData.TimeAdjM);

                        dataList.Add(curveData);
                    }
                    catch
                    {
                    }
                }

                return dataList;
            }
            catch(Exception ex)
            {
                throw new Exception($"从数据库提取曲线数据出错：{ex.Message}");
            }
        }

        #endregion
    }
}