﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 柱塞泵序列号
    ///</summary>
    [SugarTable("pump_sn_info")]
    [Description("柱塞泵序列号")]
    public class PumpSNInfo
    {
        /// <summary>
        /// 序列号
        ///</summary>
        [SugarColumn(ColumnName = "sn")]
        [Description("序列号")]
        public string? SN { get; set; }
    }
}