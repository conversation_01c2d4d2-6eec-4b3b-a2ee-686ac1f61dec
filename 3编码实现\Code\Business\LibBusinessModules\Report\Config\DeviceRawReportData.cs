﻿using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace LibBusinessModules.Report.Config
{
    /// <summary>
    /// 存储单个设备报表导出相关所有信息
    /// </summary>
    public class DeviceRawReportData
    {
        #region 字段属性

        /// <summary>
        /// 设备序列号
        ///</summary>
        [Description("设备序列号")]
        public string SNCode { get; }

        /// <summary>
        /// 设备基础信息
        /// </summary>
        [Description("设备基础信息")]
        public DeviceInfo DevInfo { get; set; } = new DeviceInfo();

        /// <summary>
        /// 光源信息
        /// </summary>
        [Description("光源信息")]
        public LightSourceInfo LightInfo { get; set; } = new LightSourceInfo();

        /// <summary>
        /// 常规测量数据
        /// </summary>
        [Description("常规测量数据")]
        public List<MeasureData> MeasureDataList { get; set; } = new List<MeasureData>();

        /// <summary>
        /// 常规校准数据
        /// </summary>
        [Description("常规校准数据")]
        public List<CalibrationData> CalibrationDataList { get; set; } = new List<CalibrationData>();

        /// <summary>
        /// 高指测量数据
        /// </summary>
        [Description("高指测量数据")]
        public List<ImnMeasureData> ImnMeasureDataList { get; set; } = new List<ImnMeasureData>();

        /// <summary>
        /// 高指校准数据
        /// </summary>
        [Description("高指校准数据")]
        public List<ImnCalibrationData> ImnCalibrationDataList { get; set; } = new List<ImnCalibrationData>();

        /// <summary>
        /// 总氮测量数据
        /// </summary>
        [Description("总氮测量数据")]
        public List<TnMeasureData> TnMeasureDataList { get; set; } = new List<TnMeasureData>();

        /// <summary>
        /// 总氮校准数据
        /// </summary>
        [Description("总氮校准数据")]
        public List<TnCalibrationData> TnCalibrationDataList { get; set; } = new List<TnCalibrationData>();

        /// <summary>
        /// 曲线数据
        /// </summary>
        [Description("曲线数据")]
        public List<CurveData> CurveDataList { get; set; } = new List<CurveData>();

        #endregion

        #region 构造

        public DeviceRawReportData(string snCode)
        {
            SNCode = snCode;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 根据设备序列号，从PC数据库提取数据
        /// </summary>

        public void GetDeviceDataFromDB()
        {
            if(string.IsNullOrEmpty(SNCode))
            {
                throw new Exception("当前设备SN码为空！");
            }
            // 1. 获取设备基础信息
            DevInfo = DBHelper.GetPCDBContext().Queryable<DeviceInfo>().Where(data => data.SNCode == SNCode).First();
            if(DevInfo == null)
            {
                throw new Exception($"当前SN码{SNCode}的设备信息不存在！");
            }
            // 2. 获取光源信息
            LightInfo = DBHelper.GetPCDBContext().Queryable<LightSourceInfo>().Where(data => data.SNCode == SNCode).First();
            if(LightInfo == null)
            {
                throw new Exception($"当前SN码{SNCode}的光源信息不存在！");
            }
            // 3. 获取曲线数据
            CurveDataList = DBHelper.GetPCDBContext().Queryable<CurveData>().Where(data => data.SNCode == SNCode).ToList();

            // 获取测量、校准数据
            // 高指
            if(DevInfo.IsIMNDevice)
            {
                ImnMeasureDataList = DBHelper.GetPCDBContext().Queryable<ImnMeasureData>().Where(data => data.SNCode == SNCode).ToList();
                ImnCalibrationDataList = DBHelper.GetPCDBContext().Queryable<ImnCalibrationData>().Where(data => data.SNCode == SNCode).ToList();
            }
            // 总氮
            else if(DevInfo.IsTNDevice)
            {
                TnMeasureDataList = DBHelper.GetPCDBContext().Queryable<TnMeasureData>().Where(data => data.SNCode == SNCode).ToList();
                TnCalibrationDataList = DBHelper.GetPCDBContext().Queryable<TnCalibrationData>().Where(data => data.SNCode == SNCode).ToList();
            }
            // 常规
            else
            {
                MeasureDataList = DBHelper.GetPCDBContext().Queryable<MeasureData>().Where(data => data.SNCode == SNCode).ToList();
                CalibrationDataList = DBHelper.GetPCDBContext().Queryable<CalibrationData>().Where(data => data.SNCode == SNCode).ToList();
            }
        }

        #endregion
    }
}