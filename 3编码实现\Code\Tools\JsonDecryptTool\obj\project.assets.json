{"version": 3, "targets": {".NETFramework,Version=v2.0": {}, ".NETFramework,Version=v2.0/win": {}, ".NETFramework,Version=v2.0/win-arm64": {}, ".NETFramework,Version=v2.0/win-x64": {}, ".NETFramework,Version=v2.0/win-x86": {}}, "libraries": {}, "projectFileDependencyGroups": {".NETFramework,Version=v2.0": []}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Tools\\JsonDecryptTool\\JsonDecryptTool.csproj", "projectName": "配置文件解密工具", "projectPath": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Tools\\JsonDecryptTool\\JsonDecryptTool.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "G:\\01-My<PERSON><PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Tools\\JsonDecryptTool\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net20"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://*************/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net20": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net20": {}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}