﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 流路版本
    ///</summary>
    [SugarTable("flow_version_info")]
    [Description("流路版本")]
    public class FlowVersionInfo
    {
        /// <summary>
        /// 版本
        ///</summary>
        [SugarColumn(ColumnName = "version")]
        [Description("版本")]
        public string? Version { get; set; }
    }
}