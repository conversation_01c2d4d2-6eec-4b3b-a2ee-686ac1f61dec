﻿using LibBusinessModules.Report.Config;
using NPOI.XWPF.UserModel;
using System;
using System.Collections.Generic;
using System.Linq;
using LibBusinessModules.Config;
using LibBaseModules.Helper;

namespace LibBusinessModules.Report.Helper
{
    /// <summary>
    /// 出厂检验记录单导出帮助类
    /// </summary>
    internal static class InspectionRecordExportHelper
    {
        /// <summary>
        /// 导出出厂检验记录单文档
        /// </summary>
        /// <param name="calculateNode">报表计算节点</param>
        public static string ExportWordFile(ReportCalculateNode calculateNode)
        {
            var outputPath = ReportPathHelper.GetReportSavePath(calculateNode, "出厂检验记录单");
            var templateFilePath = ReportPathHelper.GetTemplateFilePath(calculateNode, "出厂检验记录单");

            // 加载模板文件
            using var doc = ReportDocumentHelper.LoadDocument(templateFilePath);

            // 生成替换信息
            var replacements = PrepareReplacements(calculateNode);

            // 执行基础信息替换
            ReportDocumentHelper.ReplaceAllText(doc, replacements);

            // 插入性能检查结果表
            InsertPerformanceCheckResult(doc, calculateNode);

            // 保存结果文件
            ReportDocumentHelper.SaveDocument(doc, outputPath);

            // 返回报表文件位置
            return outputPath;
        }

        #region 辅助方法

        /// <summary>
        /// 准备所有需要替换的内容
        /// </summary>
        private static Dictionary<string, string> PrepareReplacements(ReportCalculateNode calculateNode)
        {
            // 替换固定格式信息
            var replacements = new Dictionary<string, string>
            {
                { "{{仪器ID}}", calculateNode.SNCode },
                { "{{仪器型号}}", $"{calculateNode.DeviceConfigInfo.Model}型" },
                { "{{主板软件}}", calculateNode.DevInfo.MainBoardVersion },
                { "{{流路程序}}", calculateNode.DevInfo.FlowVersion },
                { "{{屏幕软件}}", calculateNode.DevInfo.HmiVersion },
                { "{{检验依据}}", calculateNode.DeviceConfigInfo.InspectionBasis },
                { "{{检验时间}}", DateTime.Now.ToString("yyyy.MM.dd") },
                { "{{环境条件}}", FormatTemperatureAndHumidity(calculateNode.Temperature, calculateNode.Humidity) },
                { "{{检验结果}}",GetMainCheckResult(calculateNode) },
                { "{{检验员}}", calculateNode.InspectorName },
                { "{{审核员}}", calculateNode.AuditorName },
            };

            return replacements;
        }

        /// <summary>
        /// 插入性能检查结果
        /// </summary>
        private static void InsertPerformanceCheckResult(XWPFDocument doc, ReportCalculateNode calculateNode)
        {
            // 查找包含占位符的位置
            var position = ReportDocumentHelper.FindMarkerCell(doc, "{{性能检查}}");
            if(!position.HasValue)
            {
                return;
            }

            var table = position.Value.table;
            var startRowIndex = position.Value.rowIndex;

            // 获取所有测试项类型
            var measureItemTypes = Enum.GetValues(typeof(eMeasureItemType)).Cast<eMeasureItemType>().ToList();

            // 组装所有性能检查行
            var rows = new List<string[]>();
            foreach(var itemType in measureItemTypes)
            {
                var row = new string[5];
                row[0] = "";
                row[1] = itemType.GetDescription();
                row[2] = "符合检验规范";
                row[3] = GetCheckResult(calculateNode, itemType);
                row[4] = GetRemark(calculateNode, itemType);
                rows.Add(row);
            }
            if(rows.Count > 0)
            {
                rows[0][0] = "性能检查";
            }

            // 插入性能检查结果
            ReportDocumentHelper.InsertTableRowsWithMerge(table, startRowIndex, rows, 0);

            // 设置整个区域边框
            ReportDocumentHelper.SetBordersForTableRegion(table, startRowIndex, startRowIndex + rows.Count - 1);
        }

        /// <summary>
        /// 获取检查结果
        /// </summary>
        private static string GetCheckResult(ReportCalculateNode calculateNode, eMeasureItemType itemType)
        {
            // 查找标准量程测量数据中是否有对应的测试项
            var measureData = calculateNode.StandardRangeMeasureData
                .FirstOrDefault(x => x.MeasureItem.ItemType == itemType);

            if(measureData == null)
            {
                return "/";
            }

            return measureData.IsQualified ? "是" : "否";
        }

        /// <summary>
        /// 获取备注信息
        /// </summary>
        private static string GetRemark(ReportCalculateNode calculateNode, eMeasureItemType itemType)
        {
            // 查找标准量程测量数据中是否有对应的测试项
            var measureData = calculateNode.StandardRangeMeasureData
                .FirstOrDefault(x => x.MeasureItem.ItemType == itemType);

            if(measureData == null)
            {
                return "/";
            }

            return measureData.IsQualified ? "/" : "请检查";
        }

        /// <summary>
        /// 获取总检查结果
        /// 是否所有勾选项都合格
        /// </summary>
        private static string GetMainCheckResult(ReportCalculateNode calculateNode)
        {
            return calculateNode.StandardRangeMeasureData.Any(x => !x.IsQualified) ? "不合格" : "合格";
        }

        /// <summary>
        /// 格式化环境条件显示
        /// </summary>
        private static string FormatTemperatureAndHumidity(double temperature, double humidity)
        {
            string temp = double.IsNaN(temperature) ? " " : $"{temperature:F1}";
            string hum = double.IsNaN(humidity) ? " " : $"{humidity:F1}";
            return $"温度{temp}℃；湿度{hum}%RH";
        }

        #endregion
    }
}