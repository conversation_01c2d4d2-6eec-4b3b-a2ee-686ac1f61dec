﻿using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using Sunny.UI;
using System;
using System.Data;
using System.Windows.Forms;

namespace LibBusinessModules.Report.UI
{
    /// <summary>
    /// 设备选择
    /// </summary>
    public partial class UC_DeviceSelect : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 设备列表
        /// </summary>
        private DataTable _dt = new DataTable();

        /// <summary>
        /// 选中的设备序列号
        /// </summary>
        public string SelectSNCode => cmbDeviceList.Text;

        #endregion

        #region 构造

        public UC_DeviceSelect()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        /// <summary>
        /// 控件加载时初始化控件格式
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void UC_DeviceSelect_Load(object sender, System.EventArgs e)
        {
            // 初始化控件格式
            cmbDeviceList.DataGridView.Init();
            cmbDeviceList.ItemSize = new System.Drawing.Size(360, 240);
            cmbDeviceList.DataGridView.AddColumn("序列号", "SNCode");
            cmbDeviceList.DataGridView.AddColumn("设备类型", "DeviceType");
            cmbDeviceList.DataGridView.ReadOnly = true;
            cmbDeviceList.DataGridView.DataSource = _dt;
            cmbDeviceList.FilterColumnName = "SNCode"; //不设置则全部列过滤，可加分号过滤多列

            // 初始化数据源
            _dt.Columns.Add("SNCode", typeof(string));
            _dt.Columns.Add("DeviceType", typeof(string));

            //// 刷新数据源
            //Task.Run(() =>
            //{
            //    RefreshDataSource();
            //});
        }

        /// <summary>
        /// 点击时填充序列号到文本框
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="value"></param>
        private void cmbDeviceList_ValueChanged(object sender, object value)
        {
            cmbDeviceList.Text = string.Empty;
            if(value != null && value is DataGridViewRow)
            {
                DataGridViewRow row = (DataGridViewRow)value;
                cmbDeviceList.Text = row.Cells["序列号"].Value.ToString();
            }
        }

        /// <summary>
        /// 控件进入时刷新数据源
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void UC_DeviceSelect_Enter(object sender, EventArgs e)
        {
            RefreshDataSource();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新数据源
        /// </summary>
        private void RefreshDataSource()
        {
            try
            {
                // 清空数据缓存
                _dt.Rows.Clear();

                // 获取数据
                var dataList = DBHelper.GetPCDBContext().Queryable<DeviceInfo>().ToList();
                foreach(var data in dataList)
                {
                    _dt.Rows.Add(data.SNCode, data.GetDeviceType());
                }
            }
            catch(Exception e)
            {

            }
        }

        #endregion
    }
}