﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 光源信息
    ///</summary>
    [SugarTable("adj_signal_info")]
    [Description("光源信息")]
    public class AdjSignalInfo
    {
        /// <summary>
        /// 时间
        ///</summary>
        [SugarColumn(ColumnName = "time")]
        [Description("时间")]
        public string? Time { get; set; }

        /// <summary>
        /// 信号电流
        ///</summary>
        [SugarColumn(ColumnName = "current")]
        [Description("信号电流")]
        public string? Current { get; set; }

        /// <summary>
        /// 检测信号档位
        ///</summary>
        [SugarColumn(ColumnName = "main_index")]
        [Description("检测信号档位")]
        public string? MainIndex { get; set; }

        /// <summary>
        /// 检测电压1
        ///</summary>
        [SugarColumn(ColumnName = "main_v1")]
        [Description("检测电压1")]
        public string? MainV1 { get; set; }

        /// <summary>
        /// 检测电压2
        ///</summary>
        [SugarColumn(ColumnName = "main_v2")]
        [Description("检测电压2")]
        public string? MainV2 { get; set; }

        /// <summary>
        /// 检测电压3
        ///</summary>
        [SugarColumn(ColumnName = "main_v3")]
        [Description("检测电压3")]
        public string? MainV3 { get; set; }

        /// <summary>
        /// 检测电压4
        ///</summary>
        [SugarColumn(ColumnName = "main_v4")]
        [Description("检测电压4")]
        public string? MainV4 { get; set; }

        /// <summary>
        /// 参比信号档位
        ///</summary>
        [SugarColumn(ColumnName = "ref_index")]
        [Description("参比信号档位")]
        public string? RefIndex { get; set; }

        /// <summary>
        /// 参比电压1
        ///</summary>
        [SugarColumn(ColumnName = "ref_v1")]
        [Description("参比电压1")]
        public string? RefV1 { get; set; }

        /// <summary>
        /// 参比电压2
        ///</summary>
        [SugarColumn(ColumnName = "ref_v2")]
        [Description("参比电压2")]
        public string? RefV2 { get; set; }

        /// <summary>
        /// 参比电压3
        ///</summary>
        [SugarColumn(ColumnName = "ref_v3")]
        [Description("参比电压3")]
        public string? RefV3 { get; set; }

        /// <summary>
        /// 参比电压4
        ///</summary>
        [SugarColumn(ColumnName = "ref_v4")]
        [Description("参比电压4")]
        public string? RefV4 { get; set; }
    }
}