﻿using LibBaseModules.DB;
using LibBusinessModules.DB.Models.Device;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.PC
{
    /// <summary>
    /// 高指校准数据
    ///</summary>
    [SugarTable("imn_calibration_data")]
    [Description("高指校准数据")]
    public class ImnCalibrationData : BaseNode
    {
        #region 字段属性

        /// <summary>
        /// 设备序列号
        ///</summary>
        [SugarColumn(ColumnName = "sncode", IsPrimaryKey = true)]
        [Description("设备序列号")]
        public string SNCode { get; set; }

        /// <summary>
        /// 时间
        ///</summary>
        [SugarColumn(ColumnName = "time", IsPrimaryKey = true, SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "数据时间")]
        [Description("数据时间")]
        public DateTime Time { get; set; }

        /// <summary>
        /// 测量值
        ///</summary>
        [SugarColumn(ColumnName = "value", ColumnDescription = "浓度")]
        [Description("测量值")]
        public double Value { get; set; }

        /// <summary>
        /// 量程
        ///</summary>
        [SugarColumn(ColumnName = "range", ColumnDescription = "量程")]
        [Description("量程")]
        public double Range { get; set; }

        /// <summary>
        /// 数据标识
        ///</summary>
        [SugarColumn(ColumnName = "flag", ColumnDescription = "数据标识")]
        [Description("数据标识")]
        public string Flag { get; set; }

        /// <summary>
        /// 数据类型
        ///</summary>
        [SugarColumn(ColumnName = "type", ColumnDescription = "数据类型")]
        [Description("数据类型")]
        public string Type { get; set; }

        /// <summary>
        /// 修正滴定数
        ///</summary>
        [SugarColumn(ColumnName = "titration_amend", ColumnDescription = "修正滴定数")]
        [Description("修正滴定数")]
        public double TitrationAmend { get; set; }

        /// <summary>
        /// 滴定数
        ///</summary>
        [SugarColumn(ColumnName = "titration", ColumnDescription = "滴定数")]
        [Description("滴定数")]
        public double Titration { get; set; }

        /// <summary>
        /// 吸光度1
        ///</summary>
        [SugarColumn(ColumnName = "abs1", ColumnDescription = "吸光度1")]
        [Description("吸光度1")]
        public double Abs1 { get; set; }

        /// <summary>
        /// 检测信号1
        ///</summary>
        [SugarColumn(ColumnName = "signalMain1", ColumnDescription = "检测信号1")]
        [Description("检测信号1")]
        public double SignalMain1 { get; set; }

        /// <summary>
        /// 参比信号1
        ///</summary>
        [SugarColumn(ColumnName = "signalRef1", ColumnDescription = "参比信号1")]
        [Description("参比信号1")]
        public double SignalRef1 { get; set; }

        /// <summary>
        /// 吸光度2
        ///</summary>
        [SugarColumn(ColumnName = "abs2", ColumnDescription = "吸光度2")]
        [Description("吸光度2")]
        public double Abs2 { get; set; }

        /// <summary>
        /// 检测信号2
        ///</summary>
        [SugarColumn(ColumnName = "signalMain2", ColumnDescription = "检测信号2")]
        [Description("检测信号2")]
        public double SignalMain2 { get; set; }

        /// <summary>
        /// 参比信号2
        ///</summary>
        [SugarColumn(ColumnName = "signalRef2", ColumnDescription = "参比信号2")]
        [Description("参比信号2")]
        public double SignalRef2 { get; set; }

        /// <summary>
        /// 测量类型
        ///</summary>
        [SugarColumn(ColumnName = "type1", ColumnDescription = "测量类型")]
        [Description("测量类型")]
        public string Type1 { get; set; }

        #endregion

        #region 公共方法

        /// <summary>
        /// 从设备数据库生成数据
        /// </summary>
        public static List<ImnCalibrationData> InitFromDeviceDB(SqlSugarClient db, string snCode)
        {
            try
            {
                List<ImnCalibrationData> dataList = new List<ImnCalibrationData>();

                List<ImnRawAdjdata> rawAdjdataList = db.Queryable<ImnRawAdjdata>().ToList();
                foreach(ImnRawAdjdata rawAdjdata in rawAdjdataList)
                {
                    try
                    {
                        ImnCalibrationData curveData = new ImnCalibrationData();
                        curveData.SNCode = snCode;
                        curveData.Time = DateTime.Parse(rawAdjdata.Time);
                        curveData.Value = double.Parse(rawAdjdata.Value);
                        curveData.Range = double.Parse(rawAdjdata.Range);
                        curveData.Flag = rawAdjdata.Flag;
                        curveData.Type1 = rawAdjdata.Type1;
                        curveData.Type = rawAdjdata.Type;
                        curveData.TitrationAmend = double.Parse(rawAdjdata.TitrationAmend);
                        curveData.Titration = double.Parse(rawAdjdata.Titration);
                        curveData.Abs1 = double.Parse(rawAdjdata.Abs1);
                        curveData.SignalMain1 = double.Parse(rawAdjdata.SignalMain1);
                        curveData.SignalRef1 = double.Parse(rawAdjdata.SignalRef1);
                        curveData.Abs2 = double.Parse(rawAdjdata.Abs2);
                        curveData.SignalMain2 = double.Parse(rawAdjdata.SignalMain2);
                        curveData.SignalRef2 = double.Parse(rawAdjdata.SignalRef2);

                        dataList.Add(curveData);
                    }
                    catch
                    {
                    }
                }

                return dataList;
            }
            catch(Exception ex)
            {
                throw new Exception($"从数据库提取高指校准数据出错：{ex.Message}");
            }
        }

        #endregion
    }
}