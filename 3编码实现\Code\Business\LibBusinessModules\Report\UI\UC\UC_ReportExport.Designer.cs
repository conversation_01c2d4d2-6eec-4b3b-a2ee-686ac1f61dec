﻿namespace LibBusinessModules.Report.UI
{
    partial class UC_ReportExport
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.pnlTop = new Sunny.UI.UIPanel();
            this.btnStartQuery = new Sunny.UI.UIButton();
            this.pnlDataOverview = new Sunny.UI.UIPanel();
            this.gbDataOverview = new Sunny.UI.UIGroupBox();
            this.txtCalibrationDataCount = new Sunny.UI.UILabel();
            this.txtMeasureDataCount = new Sunny.UI.UILabel();
            this.txtCurveDataCount = new Sunny.UI.UILabel();
            this.uiLabel3 = new Sunny.UI.UILabel();
            this.uiLabel2 = new Sunny.UI.UILabel();
            this.uiLabel1 = new Sunny.UI.UILabel();
            this.uc_ReportDataShow = new LibBusinessModules.Report.UI.UC_ReportDataShow();
            this.uiGroupBox4 = new Sunny.UI.UIGroupBox();
            this.chkQualifiedRecord = new Sunny.UI.UICheckBox();
            this.chkInspectionRecord = new Sunny.UI.UICheckBox();
            this.btnReportExport = new Sunny.UI.UIButton();
            this.chkDataRecord = new Sunny.UI.UICheckBox();
            this.uc_DeviceSelect = new LibBusinessModules.Report.UI.UC_DeviceSelect();
            this.pnlTop.SuspendLayout();
            this.pnlDataOverview.SuspendLayout();
            this.gbDataOverview.SuspendLayout();
            this.uc_ReportDataShow.SuspendLayout();
            this.uiGroupBox4.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlTop
            // 
            this.pnlTop.Controls.Add(this.btnStartQuery);
            this.pnlTop.Controls.Add(this.pnlDataOverview);
            this.pnlTop.Controls.Add(this.uc_DeviceSelect);
            this.pnlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlTop.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlTop.Location = new System.Drawing.Point(0, 0);
            this.pnlTop.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlTop.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlTop.Name = "pnlTop";
            this.pnlTop.Padding = new System.Windows.Forms.Padding(1);
            this.pnlTop.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.pnlTop.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.pnlTop.Size = new System.Drawing.Size(1200, 104);
            this.pnlTop.TabIndex = 0;
            this.pnlTop.Text = null;
            this.pnlTop.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnStartQuery
            // 
            this.btnStartQuery.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnStartQuery.Font = new System.Drawing.Font("宋体", 12F);
            this.btnStartQuery.Location = new System.Drawing.Point(296, 65);
            this.btnStartQuery.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnStartQuery.Name = "btnStartQuery";
            this.btnStartQuery.Size = new System.Drawing.Size(92, 32);
            this.btnStartQuery.Style = Sunny.UI.UIStyle.Custom;
            this.btnStartQuery.TabIndex = 25;
            this.btnStartQuery.Text = "查询";
            this.btnStartQuery.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnStartQuery.Click += new System.EventHandler(this.btnStartQuery_Click);
            // 
            // pnlDataOverview
            // 
            this.pnlDataOverview.Controls.Add(this.gbDataOverview);
            this.pnlDataOverview.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlDataOverview.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlDataOverview.Location = new System.Drawing.Point(398, 1);
            this.pnlDataOverview.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlDataOverview.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlDataOverview.Name = "pnlDataOverview";
            this.pnlDataOverview.Padding = new System.Windows.Forms.Padding(1);
            this.pnlDataOverview.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.pnlDataOverview.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.pnlDataOverview.Size = new System.Drawing.Size(801, 102);
            this.pnlDataOverview.TabIndex = 3;
            this.pnlDataOverview.Text = "uiPanel1";
            this.pnlDataOverview.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // gbDataOverview
            // 
            this.gbDataOverview.Controls.Add(this.txtCalibrationDataCount);
            this.gbDataOverview.Controls.Add(this.txtMeasureDataCount);
            this.gbDataOverview.Controls.Add(this.txtCurveDataCount);
            this.gbDataOverview.Controls.Add(this.uiLabel3);
            this.gbDataOverview.Controls.Add(this.uiLabel2);
            this.gbDataOverview.Controls.Add(this.uiLabel1);
            this.gbDataOverview.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbDataOverview.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbDataOverview.Location = new System.Drawing.Point(1, 1);
            this.gbDataOverview.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbDataOverview.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbDataOverview.Name = "gbDataOverview";
            this.gbDataOverview.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.gbDataOverview.Size = new System.Drawing.Size(799, 100);
            this.gbDataOverview.TabIndex = 64;
            this.gbDataOverview.Text = "数据概览";
            this.gbDataOverview.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // txtCalibrationDataCount
            // 
            this.txtCalibrationDataCount.AutoSize = true;
            this.txtCalibrationDataCount.BackColor = System.Drawing.Color.Transparent;
            this.txtCalibrationDataCount.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtCalibrationDataCount.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.txtCalibrationDataCount.Location = new System.Drawing.Point(433, 49);
            this.txtCalibrationDataCount.Name = "txtCalibrationDataCount";
            this.txtCalibrationDataCount.Size = new System.Drawing.Size(15, 16);
            this.txtCalibrationDataCount.TabIndex = 5;
            this.txtCalibrationDataCount.Text = "0";
            // 
            // txtMeasureDataCount
            // 
            this.txtMeasureDataCount.AutoSize = true;
            this.txtMeasureDataCount.BackColor = System.Drawing.Color.Transparent;
            this.txtMeasureDataCount.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtMeasureDataCount.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.txtMeasureDataCount.Location = new System.Drawing.Point(276, 49);
            this.txtMeasureDataCount.Name = "txtMeasureDataCount";
            this.txtMeasureDataCount.Size = new System.Drawing.Size(15, 16);
            this.txtMeasureDataCount.TabIndex = 4;
            this.txtMeasureDataCount.Text = "0";
            // 
            // txtCurveDataCount
            // 
            this.txtCurveDataCount.AutoSize = true;
            this.txtCurveDataCount.BackColor = System.Drawing.Color.Transparent;
            this.txtCurveDataCount.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtCurveDataCount.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.txtCurveDataCount.Location = new System.Drawing.Point(119, 49);
            this.txtCurveDataCount.Name = "txtCurveDataCount";
            this.txtCurveDataCount.Size = new System.Drawing.Size(15, 16);
            this.txtCurveDataCount.TabIndex = 3;
            this.txtCurveDataCount.Text = "0";
            // 
            // uiLabel3
            // 
            this.uiLabel3.AutoSize = true;
            this.uiLabel3.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel3.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel3.Location = new System.Drawing.Point(330, 49);
            this.uiLabel3.Name = "uiLabel3";
            this.uiLabel3.Size = new System.Drawing.Size(103, 16);
            this.uiLabel3.TabIndex = 2;
            this.uiLabel3.Text = "校准数据量：";
            // 
            // uiLabel2
            // 
            this.uiLabel2.AutoSize = true;
            this.uiLabel2.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel2.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel2.Location = new System.Drawing.Point(173, 49);
            this.uiLabel2.Name = "uiLabel2";
            this.uiLabel2.Size = new System.Drawing.Size(103, 16);
            this.uiLabel2.TabIndex = 1;
            this.uiLabel2.Text = "测量数据量：";
            // 
            // uiLabel1
            // 
            this.uiLabel1.AutoSize = true;
            this.uiLabel1.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel1.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel1.Location = new System.Drawing.Point(16, 49);
            this.uiLabel1.Name = "uiLabel1";
            this.uiLabel1.Size = new System.Drawing.Size(103, 16);
            this.uiLabel1.TabIndex = 0;
            this.uiLabel1.Text = "曲线数据量：";
            // 
            // uc_ReportDataShow
            // 
            this.uc_ReportDataShow.Controls.Add(this.uiGroupBox4);
            this.uc_ReportDataShow.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_ReportDataShow.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_ReportDataShow.Location = new System.Drawing.Point(0, 104);
            this.uc_ReportDataShow.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_ReportDataShow.Name = "uc_ReportDataShow";
            this.uc_ReportDataShow.Padding = new System.Windows.Forms.Padding(1);
            this.uc_ReportDataShow.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_ReportDataShow.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_ReportDataShow.Size = new System.Drawing.Size(1200, 680);
            this.uc_ReportDataShow.TabIndex = 1;
            this.uc_ReportDataShow.Text = "日志查询";
            this.uc_ReportDataShow.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uiGroupBox4
            // 
            this.uiGroupBox4.Controls.Add(this.chkQualifiedRecord);
            this.uiGroupBox4.Controls.Add(this.chkInspectionRecord);
            this.uiGroupBox4.Controls.Add(this.btnReportExport);
            this.uiGroupBox4.Controls.Add(this.chkDataRecord);
            this.uiGroupBox4.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.uiGroupBox4.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiGroupBox4.Location = new System.Drawing.Point(1, 616);
            this.uiGroupBox4.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiGroupBox4.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiGroupBox4.Name = "uiGroupBox4";
            this.uiGroupBox4.Padding = new System.Windows.Forms.Padding(1, 32, 1, 1);
            this.uiGroupBox4.Size = new System.Drawing.Size(1198, 63);
            this.uiGroupBox4.TabIndex = 4;
            this.uiGroupBox4.Text = "数据导出";
            this.uiGroupBox4.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // chkQualifiedRecord
            // 
            this.chkQualifiedRecord.AutoSize = true;
            this.chkQualifiedRecord.BackColor = System.Drawing.Color.Transparent;
            this.chkQualifiedRecord.Checked = true;
            this.chkQualifiedRecord.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkQualifiedRecord.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkQualifiedRecord.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.chkQualifiedRecord.Location = new System.Drawing.Point(320, 30);
            this.chkQualifiedRecord.MinimumSize = new System.Drawing.Size(1, 1);
            this.chkQualifiedRecord.Name = "chkQualifiedRecord";
            this.chkQualifiedRecord.Size = new System.Drawing.Size(78, 21);
            this.chkQualifiedRecord.TabIndex = 3;
            this.chkQualifiedRecord.Text = "合格证";
            // 
            // chkInspectionRecord
            // 
            this.chkInspectionRecord.AutoSize = true;
            this.chkInspectionRecord.BackColor = System.Drawing.Color.Transparent;
            this.chkInspectionRecord.Checked = true;
            this.chkInspectionRecord.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkInspectionRecord.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkInspectionRecord.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.chkInspectionRecord.Location = new System.Drawing.Point(154, 30);
            this.chkInspectionRecord.MinimumSize = new System.Drawing.Size(1, 1);
            this.chkInspectionRecord.Name = "chkInspectionRecord";
            this.chkInspectionRecord.Size = new System.Drawing.Size(142, 21);
            this.chkInspectionRecord.TabIndex = 2;
            this.chkInspectionRecord.Text = "出厂检验记录单";
            // 
            // btnReportExport
            // 
            this.btnReportExport.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnReportExport.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnReportExport.Location = new System.Drawing.Point(428, 23);
            this.btnReportExport.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnReportExport.Name = "btnReportExport";
            this.btnReportExport.Size = new System.Drawing.Size(100, 35);
            this.btnReportExport.TabIndex = 1;
            this.btnReportExport.Text = "报告导出";
            this.btnReportExport.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnReportExport.Click += new System.EventHandler(this.btnReportExport_Click);
            // 
            // chkDataRecord
            // 
            this.chkDataRecord.AutoSize = true;
            this.chkDataRecord.BackColor = System.Drawing.Color.Transparent;
            this.chkDataRecord.Checked = true;
            this.chkDataRecord.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkDataRecord.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkDataRecord.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.chkDataRecord.Location = new System.Drawing.Point(20, 30);
            this.chkDataRecord.MinimumSize = new System.Drawing.Size(1, 1);
            this.chkDataRecord.Name = "chkDataRecord";
            this.chkDataRecord.Size = new System.Drawing.Size(110, 21);
            this.chkDataRecord.TabIndex = 0;
            this.chkDataRecord.Text = "数据记录单";
            // 
            // uc_DeviceSelect
            // 
            this.uc_DeviceSelect.Dock = System.Windows.Forms.DockStyle.Left;
            this.uc_DeviceSelect.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_DeviceSelect.Location = new System.Drawing.Point(1, 1);
            this.uc_DeviceSelect.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_DeviceSelect.Name = "uc_DeviceSelect";
            this.uc_DeviceSelect.Padding = new System.Windows.Forms.Padding(1);
            this.uc_DeviceSelect.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_DeviceSelect.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_DeviceSelect.Size = new System.Drawing.Size(397, 102);
            this.uc_DeviceSelect.TabIndex = 64;
            this.uc_DeviceSelect.Text = "uC_DeviceSelect1";
            this.uc_DeviceSelect.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // UC_ReportExport
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.uc_ReportDataShow);
            this.Controls.Add(this.pnlTop);
            this.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "UC_ReportExport";
            this.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.Size = new System.Drawing.Size(1200, 784);
            this.Text = "报表导出";
            this.pnlTop.ResumeLayout(false);
            this.pnlDataOverview.ResumeLayout(false);
            this.gbDataOverview.ResumeLayout(false);
            this.gbDataOverview.PerformLayout();
            this.uc_ReportDataShow.ResumeLayout(false);
            this.uiGroupBox4.ResumeLayout(false);
            this.uiGroupBox4.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
        private Sunny.UI.UIPanel pnlTop;
        private Sunny.UI.UIGroupBox gbDataOverview;
        private Sunny.UI.UIPanel pnlDataOverview;
        private LibBusinessModules.Report.UI.UC_DeviceSelect uc_DeviceSelect;
        private Sunny.UI.UIButton btnStartQuery;
        private Sunny.UI.UILabel uiLabel1;
        private Sunny.UI.UILabel txtCalibrationDataCount;
        private Sunny.UI.UILabel txtMeasureDataCount;
        private Sunny.UI.UILabel txtCurveDataCount;
        private Sunny.UI.UILabel uiLabel3;
        private Sunny.UI.UILabel uiLabel2;
        private UC_ReportDataShow uc_ReportDataShow;
        private Sunny.UI.UIGroupBox uiGroupBox4;
        private Sunny.UI.UICheckBox chkQualifiedRecord;
        private Sunny.UI.UICheckBox chkInspectionRecord;
        private Sunny.UI.UIButton btnReportExport;
        private Sunny.UI.UICheckBox chkDataRecord;
    }
}
