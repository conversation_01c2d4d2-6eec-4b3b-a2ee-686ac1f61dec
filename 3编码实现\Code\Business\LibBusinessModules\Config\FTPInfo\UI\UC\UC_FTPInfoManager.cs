﻿using Sunny.UI;
using System;

namespace LibBusinessModules.Config.UI
{
    public partial class UC_FTPInfoManager : UIUserControl
    {
        #region 构造

        public UC_FTPInfoManager()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void UC_DBInfoManager_Load(object sender, EventArgs e)
        {
            RefreshUI();
        }

        /// <summary>
        /// 保存修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if(string.IsNullOrEmpty(txtServerIP.Text))
                {
                    throw new Exception("FTP服务IP不可为空！");
                }
                if(txtServerPort.Value == 0)
                {
                    throw new Exception("FTP服务端口不可为空！");
                }
                if(string.IsNullOrEmpty(txtUserName.Text))
                {
                    throw new Exception("用户名不可为空！");
                }
                if(string.IsNullOrEmpty(txtPassword.Text))
                {
                    throw new Exception("密码不可为空！");
                }
                if(string.IsNullOrEmpty(txtFilePath.Text))
                {
                    throw new Exception("数据库文件路径不可为空！");
                }

                SystemConfig.GetInstance().FTPInfo.ServerIP = txtServerIP.Text;
                SystemConfig.GetInstance().FTPInfo.ServerPort = txtServerPort.Value;
                SystemConfig.GetInstance().FTPInfo.UserName = txtUserName.Text;
                SystemConfig.GetInstance().FTPInfo.Password = txtPassword.Text;
                SystemConfig.GetInstance().FTPInfo.FilePath = txtFilePath.Text;
                SystemConfig.GetInstance().Save();

                UIMessageBox.ShowSuccess("保存成功！");
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"保存出错：{ex.Message}");
            }
        }

        /// <summary>
        /// 重置修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            SystemConfig.GetInstance().ReLoad();
            RefreshUI();
            UIMessageBox.ShowSuccess("重置修改成功！");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新数据
        /// </summary>
        public void RefreshUI()
        {
            txtServerIP.Text = SystemConfig.GetInstance().FTPInfo.ServerIP;
            txtServerPort.Value = SystemConfig.GetInstance().FTPInfo.ServerPort;
            txtUserName.Text = SystemConfig.GetInstance().FTPInfo.UserName;
            txtPassword.Text = SystemConfig.GetInstance().FTPInfo.Password;
            txtFilePath.Text = SystemConfig.GetInstance().FTPInfo.FilePath;
        }

        #endregion
    }
}