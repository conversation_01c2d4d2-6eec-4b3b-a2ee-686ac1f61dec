﻿using System;
using System.Windows.Forms;

namespace LibBaseModules.Helper
{
    /// <summary>
    /// 控件调用帮助类
    /// </summary>
    public static class ControlCallHelper
    {
        /// <summary>
        /// 跨线程更新ProgressBar控件的value属性
        /// </summary>
        /// <param name="preBar"></param>
        /// <param name="value"></param>
        public static void UpdateValue(ProgressBar preBar, int value)
        {
            if(preBar.InvokeRequired)
            {
                preBar.Invoke(new Action<int>(ctrlValue =>
                {
                    preBar.Value = ctrlValue;
                }), value);
            }
            else
            {
                preBar.Value = value;
            }
        }

        /// <summary>
        /// 跨线程更新控件的Text属性
        /// </summary>
        /// <param name="ctrl"></param>
        /// <param name="info"></param>
        public static void UpdateText(Control ctrl, string info)
        {
            if(ctrl.InvokeRequired)
            {
                ctrl.Invoke(new Action<string>(txt => { ctrl.Text = txt; }), info);
            }
            else
            {
                ctrl.Text = info;
            }
        }

        /// <summary>
        /// 跨线程更新控件的Enabled属性
        /// </summary>
        /// <param name="ctrl"></param>
        /// <param name="enable"></param>
        public static void UpdateEnable(Control ctrl, bool enable)
        {
            if(ctrl.InvokeRequired)
            {
                ctrl.Invoke(new Action<bool>(isEnable => { ctrl.Enabled = isEnable; }), enable);
            }
            else
            {
                ctrl.Enabled = enable;
            }
        }
    }
}
