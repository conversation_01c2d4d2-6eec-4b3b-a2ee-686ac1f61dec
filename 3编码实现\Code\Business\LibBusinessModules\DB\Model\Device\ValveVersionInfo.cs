﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 选向阀软件版本
    ///</summary>
    [SugarTable("valve_version_info")]
    [Description("选向阀软件版本")]
    public class ValveVersionInfo
    {
        /// <summary>
        /// 软件版本
        ///</summary>
        [SugarColumn(ColumnName = "version")]
        [Description("软件版本")]
        public string? Version { get; set; }
    }
}