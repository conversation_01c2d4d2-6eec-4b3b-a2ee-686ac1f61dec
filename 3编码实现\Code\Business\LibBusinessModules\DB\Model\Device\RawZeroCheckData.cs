﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 零点核查数据
    ///</summary>
    [SugarTable("zerocheckData")]
    [Description("零点核查数据")]
    public class RawZeroCheckData
    {
        /// <summary>
        /// 时间
        ///</summary>
        [SugarColumn(ColumnName = "time")]
        [Description("时间")]
        public string? Time { get; set; }

        /// <summary>
        /// 测量值
        ///</summary>
        [SugarColumn(ColumnName = "value")]
        [Description("测量值")]
        public string? Value { get; set; }

        /// <summary>
        /// 标准值
        ///</summary>
        [SugarColumn(ColumnName = "standard")]
        [Description("标准值")]
        public string? Standard { get; set; }

        /// <summary>
        /// 误差
        ///</summary>
        [SugarColumn(ColumnName = "err")]
        [Description("误差")]
        public string? Err { get; set; }

        /// <summary>
        /// 误差判定
        ///</summary>
        [SugarColumn(ColumnName = "judge")]
        [Description("误差判定")]
        public string? Judge { get; set; }

        /// <summary>
        /// 漂移
        ///</summary>
        [SugarColumn(ColumnName = "errDif")]
        [Description("漂移")]
        public string? ErrDif { get; set; }

        /// <summary>
        /// 漂移判定
        ///</summary>
        [SugarColumn(ColumnName = "judgeDif")]
        [Description("漂移判定")]
        public string? JudgeDif { get; set; }

        /// <summary>
        /// 上次零点样时间
        ///</summary>
        [SugarColumn(ColumnName = "time1")]
        [Description("上次零点样时间")]
        public string? Time1 { get; set; }

        /// <summary>
        /// 本次零点样时间
        ///</summary>
        [SugarColumn(ColumnName = "time2")]
        [Description("本次零点样时间")]
        public string? Time2 { get; set; }
    }
}