﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 总氮测量数据
    ///</summary>
    [SugarTable("TN_measureData")]
    [Description("总氮测量数据")]
    public class TnRawMeasuredata
    {
        /// <summary>
        /// 时间
        ///</summary>
        [SugarColumn(ColumnName = "time")]
        [Description("时间")]
        public string? Time { get; set; }

        /// <summary>
        /// 浓度
        ///</summary>
        [SugarColumn(ColumnName = "value")]
        [Description("浓度")]
        public string? Value { get; set; }

        /// <summary>
        /// 量程
        ///</summary>
        [SugarColumn(ColumnName = "range")]
        [Description("量程")]
        public string? Range { get; set; }

        /// <summary>
        /// 数据标识
        ///</summary>
        [SugarColumn(ColumnName = "flag")]
        [Description("数据标识")]
        public string? Flag { get; set; }

        /// <summary>
        /// 类型1
        ///</summary>
        [SugarColumn(ColumnName = "type1")]
        [Description("类型1")]
        public string? Type1 { get; set; }

        /// <summary>
        /// 类型2
        ///</summary>
        [SugarColumn(ColumnName = "type")]
        [Description("类型2")]
        public string? Type { get; set; }

        /// <summary>
        /// 吸光度
        ///</summary>
        [SugarColumn(ColumnName = "abs")]
        [Description("吸光度")]
        public string? Abs { get; set; }

        /// <summary>
        /// 吸光度220
        ///</summary>
        [SugarColumn(ColumnName = "abs1")]
        [Description("吸光度220")]
        public string? Abs1 { get; set; }

        /// <summary>
        /// 吸光度275
        ///</summary>
        [SugarColumn(ColumnName = "abs2")]
        [Description("吸光度275")]
        public string? Abs2 { get; set; }

        /// <summary>
        /// V1_220
        ///</summary>
        [SugarColumn(ColumnName = "signal2201")]
        [Description("V1_220")]
        public string? Signal2201 { get; set; }

        /// <summary>
        /// V1_275
        ///</summary>
        [SugarColumn(ColumnName = "signal2751")]
        [Description("V1_275")]
        public string? Signal2751 { get; set; }

        /// <summary>
        /// V2_220
        ///</summary>
        [SugarColumn(ColumnName = "signal2202")]
        [Description("V2_220")]
        public string? Signal2202 { get; set; }

        /// <summary>
        /// V2_275
        ///</summary>
        [SugarColumn(ColumnName = "signal2752")]
        [Description("V2_275")]
        public string? Signal2752 { get; set; }
    }
}