﻿using LibBusinessModules.DB.Models.Device;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.PC
{
    /// <summary>
    /// 加标回收数据
    ///</summary>
    [SugarTable("addcheck_data")]
    [Description("加标回收数据")]
    public class AddCheckData
    {
        #region 字段属性

        /// <summary>
        /// 设备序列号
        ///</summary>
        [SugarColumn(ColumnName = "sncode", IsPrimaryKey = true)]
        [Description("设备序列号")]
        public string SNCode { get; set; }

        /// <summary>
        /// 数据时间
        ///</summary>
        [SugarColumn(ColumnName = "time", IsPrimaryKey = true, SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "数据时间")]
        [Description("数据时间")]
        public DateTime Time { get; set; }

        /// <summary>
        /// 加标前测量值
        ///</summary>
        [SugarColumn(ColumnName = "value1", ColumnDescription = "加标前测量值")]
        [Description("加标前测量值")]
        public double Value1 { get; set; }

        /// <summary>
        /// 加标后测量值
        ///</summary>
        [SugarColumn(ColumnName = "value2", ColumnDescription = "加标后测量值")]
        [Description("加标后测量值")]
        public double Value2 { get; set; }

        /// <summary>
        /// 加标体积
        ///</summary>
        [SugarColumn(ColumnName = "addVolume", ColumnDescription = "加标体积")]
        [Description("加标体积")]
        public double AddVolume { get; set; }

        /// <summary>
        /// 标准值
        ///</summary>
        [SugarColumn(ColumnName = "standard", ColumnDescription = "标准值")]
        [Description("标准值")]
        public double Standard { get; set; }

        /// <summary>
        /// 回收率
        ///</summary>
        [SugarColumn(ColumnName = "rate", ColumnDescription = "回收率")]
        [Description("回收率")]
        public double Rate { get; set; }

        /// <summary>
        /// 回收判定
        ///</summary>
        [SugarColumn(ColumnName = "judge", ColumnDescription = "回收判定")]
        [Description("回收判定")]
        public string Judge { get; set; }

        /// <summary>
        /// 加标前采样时间
        ///</summary>
        [SugarColumn(ColumnName = "time1", ColumnDescription = "加标前采样时间")]
        [Description("加标前采样时间")]
        public DateTime Time1 { get; set; }

        /// <summary>
        /// 加标后采样时间
        ///</summary>
        [SugarColumn(ColumnName = "time2", ColumnDescription = "加标后采样时间")]
        [Description("加标后采样时间")]
        public DateTime Time2 { get; set; }

        #endregion

        #region 公共方法

        /// <summary>
        /// 从设备数据库生成数据
        /// </summary>
        public static List<AddCheckData> InitFromDeviceDB(SqlSugarClient db, string snCode)
        {
            try
            {
                var dataList = new List<AddCheckData>();

                List<RawAddCheckData> rawAddDataList = db.Queryable<RawAddCheckData>().ToList();
                foreach(var rawAddData in rawAddDataList)
                {
                    try
                    {
                        AddCheckData addData = new AddCheckData();
                        addData.SNCode = snCode;
                        addData.Time = DateTime.Parse(rawAddData.Time);
                        addData.Value1 = double.Parse(rawAddData.Value1);
                        addData.Value2 = double.Parse(rawAddData.Value2);
                        addData.AddVolume = double.Parse(rawAddData.AddVolume);
                        addData.Standard = double.Parse(rawAddData.Standard);
                        addData.Rate = double.Parse(rawAddData.Rate);
                        addData.Judge = rawAddData.Judge;
                        addData.Time1 = DateTime.Parse(rawAddData.Time1);
                        addData.Time2 = DateTime.Parse(rawAddData.Time2);

                        dataList.Add(addData);
                    }
                    catch
                    {
                    }
                }

                return dataList;
            }
            catch(Exception ex)
            {
                throw new Exception($"从数据库提取加标回收数据出错：{ex.Message}");
            }
        }

        #endregion
    }
}