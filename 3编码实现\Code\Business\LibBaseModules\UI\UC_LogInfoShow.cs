﻿using LibBaseModules.Helper;
using Sunny.UI;
using System;
using System.Text;
using System.Windows.Forms;

namespace LibBaseModules.UI
{
    /// <summary>
    /// 实时日志查看界面
    /// </summary>
    public partial class UC_LogInfoShow : UIUserControl
    {
        #region 构造

        /// <summary>
        /// 构造
        /// </summary>
        public UC_LogInfoShow()
        {
            InitializeComponent();
            LogUtil.GetInstance().OnLogOutEvent += AddlvLogInfo;
        }

        #endregion

        #region 日志信息

        /// <summary>
        /// 最大日志数
        /// </summary>
        private readonly int _maxCountPerKind = 1000;

        /// <summary>
        /// 添加日志信息
        /// </summary>
        /// <param name="msg"></param>
        private void AddlvLogInfo(Msg msg)
        {
            if(lvLog.InvokeRequired)
            {
                if(lvLog.Items.Count >= _maxCountPerKind)
                {
                    lvLog.Invoke(new Action<string>((tt) =>
                    {
                        lvLog.Items.RemoveAt(0);
                    }));
                }

                lvLog.Invoke(new Action<Msg>(ms =>
                {
                    lvLog.Items.Add(ms.ToString());
                    lvLog.SelectedIndex = lvLog.Items.Count - 1;
                }), msg);
            }
            else
            {
                if(lvLog.Items.Count > _maxCountPerKind)
                {
                    lvLog.Items.RemoveAt(0);
                }
                lvLog.Items.Add(msg.ToString());
                lvLog.SelectedIndex = lvLog.Items.Count - 1;
            }
        }

        private void tsbCopy_Click(object sender, EventArgs e)
        {
            if(lvLog.SelectedItems.Count > 0)
            {
                var sb = new StringBuilder();
                foreach(object t in lvLog.SelectedItems)
                {
                    sb.AppendLine(t.ToString());
                }

                string logInfo = sb.ToString();

                if(!string.IsNullOrEmpty(logInfo))
                {
                    Clipboard.SetText(sb.ToString());
                }
            }
        }

        private void tsbClear_Click(object sender, EventArgs e)
        {
            this.lvLog.Items.Clear();
        }

        #endregion
    }
}