﻿using LibBaseModules.Helper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace LibBusinessModules.Config
{
    /// <summary>
    /// 设备配置
    /// </summary>
    public class DeviceConfig
    {
        #region 字段属性

        /// <summary>
        /// 因子类型
        /// </summary>
        [Description("因子类型")]
        public string Factor { get; set; }

        /// <summary>
        /// 设备型号
        /// </summary>
        [Description("设备型号")]
        public string Model { get; set; }

        /// <summary>
        /// ID识别码
        /// </summary>
        [Description("ID识别码")]
        public string IdCode { get; set; }

        /// <summary>
        /// 检验依据
        /// </summary>
        [Description("检验依据")]
        public string InspectionBasis { get; set; } = "Q/FA 01-2022";

        /// <summary>
        /// 测试标准量程
        /// </summary>
        [Description("测试标准量程")]
        public double StandardRange { get; set; }

        /// <summary>
        /// 测试标准量程单位
        /// </summary>
        [Description("测试标准量程单位")]
        public string StandardRangeUnit { get; set; } = "mg/L";

        /// <summary>
        /// 测试辅助量程
        /// </summary>
        [Description("测试辅助量程")]
        public double AuxiliaryRange { get; set; }

        /// <summary>
        /// 测试辅助量程单位
        /// </summary>
        [Description("测试辅助量程单位")]
        public string AuxiliaryRangeUnit { get; set; } = "mg/L";

        /// <summary>
        /// 标准量程测试项
        /// </summary>
        [Description("标准量程测试项")]
        public List<MeasureItem> StandardRangeMeasureItems { get; set; } = new List<MeasureItem>();

        /// <summary>
        /// 辅助量程测试项
        /// </summary>
        [Description("辅助量程测试项")]
        public List<MeasureItem> AuxiliaryRangeMeasureItems { get; set; } = new List<MeasureItem>();

        #endregion

        #region 公共方法

        /// <summary>
        /// 初始化测量项配置
        /// 不在构造中添加，防止反序列化时重复添加
        /// </summary>
        public void InitMeasureItems()
        {
            StandardRangeMeasureItems.Clear();
            AuxiliaryRangeMeasureItems.Clear();

            // 初始化各量程测试项
            foreach(eMeasureItemType itemType in Enum.GetValues(typeof(eMeasureItemType)))
            {
                StandardRangeMeasureItems.Add(new MeasureItem(itemType));
                AuxiliaryRangeMeasureItems.Add(new MeasureItem(itemType));
            }
        }

        #endregion
    }

    /// <summary>
    /// 测试项
    /// </summary>
    public class MeasureItem
    {
        #region 字段属性

        /// <summary>
        /// 测试项类型
        /// </summary>
        public eMeasureItemType ItemType { get; set; }

        /// <summary>
        /// 测试项名称
        /// </summary>
        [JsonIgnore]
        public string Name => ItemType.GetDescription();

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsUsed { get; set; }

        /// <summary>
        /// 浓度值
        /// </summary>
        public double StandValue { get; set; }

        /// <summary>
        /// 是否绝对误差
        /// </summary>
        [JsonIgnore]
        public bool IsAbsolute
        {
            get
            {
                switch(ItemType)
                {
                    case eMeasureItemType.ZeroPointDrift:
                        return true;
                    case eMeasureItemType.Repeatability:
                    case eMeasureItemType.DemonstrationValueError1:
                    case eMeasureItemType.DemonstrationValueError2:
                    case eMeasureItemType.Linearity:
                    case eMeasureItemType.GlucoseTest:
                    case eMeasureItemType.MeasurementRangeDrift:
                    case eMeasureItemType.LowConcentrationDrift24h:
                    case eMeasureItemType.HighConcentrationDrift24h:
                    default:
                        return false;
                }
            }
        }

        /// <summary>
        /// 合格标准
        /// 相对误差：%；绝对误差：跟随量程单位
        /// </summary>
        public double QualifiedStandard { get; set; }

        /// <summary>
        /// 计算所需数据量
        /// </summary>
        [JsonIgnore]
        public int CalculableDataCount
        {
            get
            {
                int count = 0;
                switch(ItemType)
                {
                    case eMeasureItemType.Repeatability:
                    case eMeasureItemType.DemonstrationValueError1:
                    case eMeasureItemType.DemonstrationValueError2:
                    case eMeasureItemType.Linearity:
                    case eMeasureItemType.GlucoseTest:
                    case eMeasureItemType.MeasurementRangeDrift:
                        count = 6;
                        break;
                    case eMeasureItemType.ZeroPointDrift:
                    case eMeasureItemType.LowConcentrationDrift24h:
                    case eMeasureItemType.HighConcentrationDrift24h:
                        count = 24;
                        break;
                }
                return count;
            }
        }

        #endregion

        #region 构造

        public MeasureItem(eMeasureItemType itemType)
        {
            ItemType = itemType;
        }

        #endregion
    }

    /// <summary>
    /// 测试项类型
    /// </summary>
    public enum eMeasureItemType
    {
        /// <summary>
        /// 重复性
        /// </summary>
        [Description("重复性")]
        Repeatability,

        /// <summary>
        /// 示值误差1
        /// </summary>
        [Description("示值误差1")]
        DemonstrationValueError1,

        /// <summary>
        /// 示值误差2
        /// </summary>
        [Description("示值误差2")]
        DemonstrationValueError2,

        /// <summary>
        /// 直线性
        /// </summary>
        [Description("直线性")]
        Linearity,

        /// <summary>
        /// 葡萄糖试验
        /// </summary>
        [Description("葡萄糖试验")]
        GlucoseTest,

        /// <summary>
        /// 零点漂移
        /// </summary>
        [Description("零点漂移")]
        ZeroPointDrift,

        /// <summary>
        /// 量程漂移
        /// </summary>
        [Description("量程漂移")]
        MeasurementRangeDrift,

        /// <summary>
        /// 24h低浓度漂移
        /// </summary>
        [Description("24h低浓度漂移")]
        LowConcentrationDrift24h,

        /// <summary>
        /// 24h高浓度漂移
        /// </summary>
        [Description("24h高浓度漂移")]
        HighConcentrationDrift24h
    }
}
