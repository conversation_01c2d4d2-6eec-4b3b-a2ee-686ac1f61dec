﻿using System;
using System.ComponentModel;
using System.Reflection;

namespace LibBaseModules.Helper
{
    public static class ExtensionsMethods
    {
        /// <summary>
        /// 将 DateTime 对象格式化为 "yyyy-MM-dd HH:mm:ss" 字符串。
        /// </summary>
        /// <param name="dateTime">要格式化的 DateTime 对象。</param>
        /// <returns>格式化后的字符串。</returns>
        public static string ToDisplayFormat(this DateTime? dateTime)
        {
            return dateTime?.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 将 DateTime 对象格式化为 "yyyy-MM-dd HH:mm:ss" 字符串。
        /// </summary>
        /// <param name="dateTime">要格式化的 DateTime 对象。</param>
        /// <returns>格式化后的字符串。</returns>
        public static string ToDisplayFormat(this DateTime dateTime)
        {
            return dateTime.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 获取枚举值的描述文本
        /// </summary>
        /// <param name="enumValue">The enum value</param>
        /// <returns>Description text or enum name if no Description attribute</returns>
        public static string GetDescription(this Enum enumValue)
        {
            // 获取枚举的类型
            Type type = enumValue.GetType();

            // 获取枚举值的名称
            string name = Enum.GetName(type, enumValue);

            // 查找此枚举值的字段信息
            FieldInfo field = type.GetField(name);

            // 检查描述属性是否存在
            DescriptionAttribute attribute =
                (DescriptionAttribute)Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute));

            // 如果存在，则返回描述，否则返回枚举名称
            return attribute?.Description ?? name;
        }
    }
}