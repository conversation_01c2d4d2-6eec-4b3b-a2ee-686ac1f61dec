﻿using LibBaseModules.DB;
using LibBusinessModules.DB.Models.PC;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace LibBusinessModules.DB
{
    /// <summary>
    /// 存储单个设备的所有信息
    /// </summary>
    public class OneDeviceAllInfo : BaseNode
    {
        #region 字段属性

        /// <summary>
        /// 设备基础信息
        /// </summary>
        [Description("设备基础信息")]
        public DeviceInfo DevInfo { get; set; } = new DeviceInfo();

        /// <summary>
        /// 光源信息
        /// </summary>
        [Description("光源信息")]
        public LightSourceInfo LightInfo { get; set; } = new LightSourceInfo();

        /// <summary>
        /// 常规测量数据
        /// </summary>
        [Description("常规测量数据")]
        public List<MeasureData> MeasureDataList { get; set; } = new List<MeasureData>();

        /// <summary>
        /// 常规校准数据
        /// </summary>
        [Description("常规校准数据")]
        public List<CalibrationData> CalibrationDataList { get; set; } = new List<CalibrationData>();

        /// <summary>
        /// 高指测量数据
        /// </summary>
        [Description("高指测量数据")]
        public List<ImnMeasureData> ImnMeasureDataList { get; set; } = new List<ImnMeasureData>();

        /// <summary>
        /// 高指校准数据
        /// </summary>
        [Description("高指校准数据")]
        public List<ImnCalibrationData> ImnCalibrationDataList { get; set; } = new List<ImnCalibrationData>();

        /// <summary>
        /// 总氮测量数据
        /// </summary>
        [Description("总氮测量数据")]
        public List<TnMeasureData> TnMeasureDataList { get; set; } = new List<TnMeasureData>();

        /// <summary>
        /// 总氮校准数据
        /// </summary>
        [Description("总氮校准数据")]
        public List<TnCalibrationData> TnCalibrationDataList { get; set; } = new List<TnCalibrationData>();

        /// <summary>
        /// 曲线数据
        /// </summary>
        [Description("曲线数据")]
        public List<CurveData> CurveDataList { get; set; } = new List<CurveData>();

        /// <summary>
        /// 加标回收数据
        /// </summary>
        [Description("加标回收数据")]
        public List<AddCheckData> AddCheckDataList { get; set; } = new List<AddCheckData>();

        /// <summary>
        /// 平行样测试数据
        /// </summary>
        [Description("平行样测试数据")]
        public List<DoubleCheckData> DoubleCheckDataList { get; set; } = new List<DoubleCheckData>();

        /// <summary>
        /// 零点核查数据
        /// </summary>
        [Description("零点核查数据")]
        public List<ZeroCheckData> ZeroCheckDataList { get; set; } = new List<ZeroCheckData>();

        /// <summary>
        /// 跨度核查数据
        /// </summary>
        [Description("跨度核查数据")]
        public List<SpanCheckData> SpanCheckDataList { get; set; } = new List<SpanCheckData>();

        /// <summary>
        /// 操作日志数据
        /// </summary>
        [Description("操作日志数据")]
        public List<OperData> OperDataList { get; set; } = new List<OperData>();

        /// <summary>
        /// 报警记录数据
        /// </summary>
        [Description("报警记录数据")]
        public List<AlarmData> AlarmDataList { get; set; } = new List<AlarmData>();

        #endregion

        #region 构造

        public OneDeviceAllInfo()
        {

        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 从设备数据库生成数据
        /// </summary>
        /// <param name="db"></param>
        internal void InitFromDeviceDB(SqlSugarClient db)
        {
            try
            {
                db.Open();

                // 设备基础信息
                DevInfo.InitFromDeviceDB(db);

                // 光源信息
                LightInfo.InitFromDeviceDB(db, DevInfo.SNCode);

                // 总氮仪表
                if(DevInfo.SNCode.ToLower().StartsWith("fa0104"))
                {
                    // 测量数据
                    TnMeasureDataList.AddRange(TnMeasureData.InitFromDeviceDB(db, DevInfo.SNCode));
                    // 校准数据
                    TnCalibrationDataList.AddRange(TnCalibrationData.InitFromDeviceDB(db, DevInfo.SNCode));
                }
                // 高指仪表
                else if(DevInfo.SNCode.ToLower().StartsWith("fa0105"))
                {
                    // 测量数据
                    ImnMeasureDataList.AddRange(ImnMeasureData.InitFromDeviceDB(db, DevInfo.SNCode));
                    // 校准数据
                    ImnCalibrationDataList.AddRange(ImnCalibrationData.InitFromDeviceDB(db, DevInfo.SNCode));
                }
                // 其他仪表
                else
                {
                    // 测量数据
                    MeasureDataList.AddRange(MeasureData.InitFromDeviceDB(db, DevInfo.SNCode));
                    // 校准数据
                    CalibrationDataList.AddRange(CalibrationData.InitFromDeviceDB(db, DevInfo.SNCode));
                }

                // 曲线数据
                CurveDataList.AddRange(CurveData.InitFromDeviceDB(db, DevInfo.SNCode));

                // 加标回收数据
                AddCheckDataList.AddRange(AddCheckData.InitFromDeviceDB(db, DevInfo.SNCode));

                // 平行样测试数据
                DoubleCheckDataList.AddRange(DoubleCheckData.InitFromDeviceDB(db, DevInfo.SNCode));

                // 零点核查数据
                ZeroCheckDataList.AddRange(ZeroCheckData.InitFromDeviceDB(db, DevInfo.SNCode));

                // 跨度核查数据
                SpanCheckDataList.AddRange(SpanCheckData.InitFromDeviceDB(db, DevInfo.SNCode));

                // 操作日志数据
                OperDataList.AddRange(OperData.InitFromDeviceDB(db, DevInfo.SNCode));

                // 报警记录数据
                AlarmDataList.AddRange(AlarmData.InitFromDeviceDB(db, DevInfo.SNCode));
            }
            catch(Exception ex)
            {
                throw new Exception($"从数据库提取设备信息出错：{ex.Message}");
            }
            finally
            {
                db.Close();
            }
        }

        /// <summary>
        /// 保存当前数据到PC数据库
        /// </summary>
        internal void SaveToPCDB()
        {
            try
            {
                // Sqlite,需先清空部分表数据，否则会插入报错
                if(DBHelper.GetDbType() == DbType.Sqlite)
                {
                    // 曲线数据
                    DBHelper.GetPCDBContext().Deleteable<CurveData>().Where(o => o.SNCode == DevInfo.SNCode).ExecuteCommand();
                    // 加标回收数据
                    DBHelper.GetPCDBContext().Deleteable<AddCheckData>().Where(o => o.SNCode == DevInfo.SNCode).ExecuteCommand();
                    // 平行样测试数据
                    DBHelper.GetPCDBContext().Deleteable<DoubleCheckData>().Where(o => o.SNCode == DevInfo.SNCode).ExecuteCommand();
                    // 零点核查数据
                    DBHelper.GetPCDBContext().Deleteable<ZeroCheckData>().Where(o => o.SNCode == DevInfo.SNCode).ExecuteCommand();
                    // 跨度核查数据
                    DBHelper.GetPCDBContext().Deleteable<SpanCheckData>().Where(o => o.SNCode == DevInfo.SNCode).ExecuteCommand();
                    // 测量数据
                    DBHelper.GetPCDBContext().Deleteable<MeasureData>().Where(o => o.SNCode == DevInfo.SNCode).ExecuteCommand();
                    // 校准数据
                    DBHelper.GetPCDBContext().Deleteable<CalibrationData>().Where(o => o.SNCode == DevInfo.SNCode).ExecuteCommand();
                    // 总氮测量数据
                    DBHelper.GetPCDBContext().Deleteable<TnMeasureData>().Where(o => o.SNCode == DevInfo.SNCode).ExecuteCommand();
                    // 总氮校准数据
                    DBHelper.GetPCDBContext().Deleteable<TnCalibrationData>().Where(o => o.SNCode == DevInfo.SNCode).ExecuteCommand();
                    // 高指测量数据
                    DBHelper.GetPCDBContext().Deleteable<ImnMeasureData>().Where(o => o.SNCode == DevInfo.SNCode).ExecuteCommand();
                    // 高指校准数据
                    DBHelper.GetPCDBContext().Deleteable<ImnCalibrationData>().Where(o => o.SNCode == DevInfo.SNCode).ExecuteCommand();
                }

                // 设备基础信息
                // 存在数据库则更新，不存在则插入
                DBHelper.GetPCDBContext().Storageable(DevInfo).ExecuteCommand();

                // 光源信息
                DBHelper.GetPCDBContext().Storageable(LightInfo).ExecuteCommand();

                // 曲线数据
                DBHelper.GetPCDBContext().Storageable(CurveDataList).ExecuteCommand();

                // 加标回收数据
                DBHelper.GetPCDBContext().Storageable(AddCheckDataList).ExecuteCommand();

                // 平行样测试数据
                DBHelper.GetPCDBContext().Storageable(DoubleCheckDataList).ExecuteCommand();

                // 零点核查数据
                DBHelper.GetPCDBContext().Storageable(ZeroCheckDataList).ExecuteCommand();

                // 跨度核查数据
                DBHelper.GetPCDBContext().Storageable(SpanCheckDataList).ExecuteCommand();

                // 操作日志数据（先删除，再插入）
                DBHelper.GetPCDBContext().Deleteable<OperData>().Where(o => o.SNCode == DevInfo.SNCode).ExecuteCommand();
                DBHelper.GetPCDBContext().Insertable(OperDataList).ExecuteCommand();

                // 报警记录数据（先删除，再插入）
                DBHelper.GetPCDBContext().Deleteable<AlarmData>().Where(a => a.SNCode == DevInfo.SNCode).ExecuteCommand();
                DBHelper.GetPCDBContext().Insertable(AlarmDataList).ExecuteCommand();

                // 总氮仪表
                if(DevInfo.IsTNDevice)
                {
                    // 测量数据
                    DBHelper.GetPCDBContext().Storageable(TnMeasureDataList).ExecuteCommand();
                    // 校准数据
                    DBHelper.GetPCDBContext().Storageable(TnCalibrationDataList).ExecuteCommand();
                }
                // 高指仪表
                else if(DevInfo.IsIMNDevice)
                {
                    // 测量数据
                    DBHelper.GetPCDBContext().Storageable(ImnMeasureDataList).ExecuteCommand();
                    // 校准数据
                    DBHelper.GetPCDBContext().Storageable(ImnCalibrationDataList).ExecuteCommand();
                }
                // 其他仪表
                else
                {
                    // 测量数据
                    DBHelper.GetPCDBContext().Storageable(MeasureDataList).ExecuteCommand();
                    // 校准数据
                    DBHelper.GetPCDBContext().Storageable(CalibrationDataList).ExecuteCommand();
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"保存失败：{ex.Message}");
            }
        }

        #endregion
    }
}