﻿using System;
using System.ComponentModel;

namespace LibBusinessModules.Report.Config
{
    /// <summary>
    /// 报表计算用曲线数据，不区分设备类型
    /// </summary>
    public class ReportCurveData
    {
        /// <summary>
        /// 数据时间
        ///</summary>
        [Description("数据时间")]
        public DateTime Time { get; set; }

        /// <summary>
        /// 量程
        ///</summary>
        [Description("量程")]
        public double Range { get; set; }

        /// <summary>
        /// 斜率
        ///</summary>
        [Description("斜率")]
        public double K { get; set; }

        /// <summary>
        /// 截距
        ///</summary>
        [Description("截距")]
        public double B { get; set; }

        /// <summary>
        /// 二次项
        ///</summary>
        [Description("二次项")]
        public double A2 { get; set; }
    }
}
