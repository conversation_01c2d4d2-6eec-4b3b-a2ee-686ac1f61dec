﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 设备序列号
    ///</summary>
    [SugarTable("device_sn_info")]
    [Description("设备序列号")]
    public class DeviceSNInfo
    {
        /// <summary>
        /// 序列号
        ///</summary>
        [SugarColumn(ColumnName = "sn")]
        [Description("序列号")]
        public string? SN { get; set; }
    }
}