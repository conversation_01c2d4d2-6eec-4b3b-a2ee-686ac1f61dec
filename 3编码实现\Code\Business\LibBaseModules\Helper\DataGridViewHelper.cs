﻿using System;
using System.ComponentModel;
using System.Reflection;
using System.Windows.Forms;

namespace LibBaseModules.Helper
{
    /// <summary>
    /// DataGridView辅助类
    /// </summary>
    public static class DataGridViewHelper
    {
        /// <summary>
        /// 添加选择列
        /// </summary>
        /// <param name="dgvMeasureData"></param>

        public static void AddSelectColumn(DataGridView dgvMeasureData)
        {
            // 检查是否已经存在复选框列
            foreach(DataGridViewColumn column in dgvMeasureData.Columns)
            {
                if(column is DataGridViewCheckBoxColumn && column.Name == "Select")
                {
                    return; // 如果已经存在，则不再添加
                }
            }
            // 添加复选框列
            DataGridViewCheckBoxColumn checkBoxColumn = new DataGridViewCheckBoxColumn
            {
                Name = "Select",
                HeaderText = "选择",
                Width = 50,
                ReadOnly = false
            };
            dgvMeasureData.Columns.Add(checkBoxColumn);
        }

        /// <summary>
        /// 设置DataGridView列ReadOnly属性
        /// </summary>
        /// <param name="dataGridView"></param>
        /// <param name="type"></param>
        public static void SetColumnHeadersReadOnlyProperty(DataGridView dataGridView, bool flag)
        {
            foreach(DataGridViewColumn column in dataGridView.Columns)
            {
                column.ReadOnly = flag;
            }
        }

        /// <summary>
        /// 因此DataGridView某个特定列
        /// </summary>
        /// <param name="dataGridView"></param>
        /// <param name="columnId"></param>
        public static void HideSpecifyColumn(DataGridView dataGridView, string columnId)
        {
            try
            {
                dataGridView.Columns[columnId].Visible = false;
            }
            catch
            {
            }
        }

        /// <summary>
        /// 设置DataGridView列标题为属性描述
        /// </summary>
        /// <param name="dataGridView"></param>
        /// <param name="type"></param>
        public static void SetColumnHeadersFromDescriptions(DataGridView dataGridView, Type type)
        {
            foreach(DataGridViewColumn column in dataGridView.Columns)
            {
                var property = type.GetProperty(column.DataPropertyName);
                if(property != null)
                {
                    column.HeaderText = GetPropertyDescription(property);
                    column.ReadOnly = true;
                }
            }
        }

        /// <summary>
        /// 获取属性的描述信息
        /// </summary>
        /// <param name="property"></param>
        /// <returns></returns>
        private static string GetPropertyDescription(PropertyInfo property)
        {
            var descriptionAttribute = property.GetCustomAttribute<DescriptionAttribute>();
            return descriptionAttribute?.Description ?? property.Name;
        }
    }
}
