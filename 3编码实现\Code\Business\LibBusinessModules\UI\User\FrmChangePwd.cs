﻿using LibBusinessModules.Helper;
using Sunny.UI;
using System;
using System.Windows.Forms;

namespace LibBusinessModules.UI.User
{
    /// <summary>
    /// 修改密码界面
    /// </summary>
    partial class FrmChangePwd : UIForm
    {
        #region 构造

        public FrmChangePwd()
        {
            InitializeComponent();
            txtUserName.Text = GlobalHelper.CurrentSystemConfig.UserName;
        }

        #endregion

        #region 事件

        private void btnOk_Click(object sender, EventArgs e)
        {
            try
            {
                if(txtOldPwd.Text != GlobalHelper.CurrentSystemConfig.PassWord)
                {
                    throw new Exception("原密码输入错误！");
                }

                if(string.IsNullOrEmpty(txtNewPwd.Text))
                {
                    throw new Exception("新密码不可为空！");
                }
                if(txtNewPwd.Text != txtNewPwd2.Text)
                {
                    throw new Exception("新密码输入不一致！");
                }

                GlobalHelper.CurrentSystemConfig.PassWord = txtNewPwd.Text;
                GlobalHelper.CurrentSystemConfig.Save();

                UIMessageBox.ShowInfo("密码修改成功！");

                DialogResult = DialogResult.OK;
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"密码修改失败：{ex.Message}");
            }
        }

        #endregion
    }
}