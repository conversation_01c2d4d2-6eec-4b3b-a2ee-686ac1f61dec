﻿using LibBusinessModules.DB.Models.Device;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.PC
{
    /// <summary>
    /// 跨度核查数据
    ///</summary>
    [SugarTable("spancheck_data")]
    [Description("跨度核查数据")]
    public class SpanCheckData
    {
        #region 字段属性

        /// <summary>
        /// 设备序列号
        ///</summary>
        [SugarColumn(ColumnName = "sncode", IsPrimaryKey = true)]
        [Description("设备序列号")]
        public string SNCode { get; set; }

        /// <summary>
        /// 数据时间
        ///</summary>
        [SugarColumn(ColumnName = "time", IsPrimaryKey = true, SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "数据时间")]
        [Description("数据时间")]
        public DateTime Time { get; set; }

        /// <summary>
        /// 测量值
        ///</summary>
        [SugarColumn(ColumnName = "value", ColumnDescription = "浓度")]
        [Description("测量值")]
        public double Value { get; set; }

        /// <summary>
        /// 标准值
        ///</summary>
        [SugarColumn(ColumnName = "standard", ColumnDescription = "标准值")]
        [Description("标准值")]
        public double Standard { get; set; }

        /// <summary>
        /// 误差
        ///</summary>
        [SugarColumn(ColumnName = "err", ColumnDescription = "误差")]
        [Description("误差")]
        public double Err { get; set; }

        /// <summary>
        /// 误差判定
        ///</summary>
        [SugarColumn(ColumnName = "judge", ColumnDescription = "误差判定")]
        [Description("误差判定")]
        public string Judge { get; set; }

        /// <summary>
        /// 漂移
        ///</summary>
        [SugarColumn(ColumnName = "errDif", ColumnDescription = "漂移")]
        [Description("漂移")]
        public double ErrDif { get; set; }

        /// <summary>
        /// 漂移判定
        ///</summary>
        [SugarColumn(ColumnName = "judgeDif", ColumnDescription = "漂移判定")]
        [Description("漂移判定")]
        public string JudgeDif { get; set; }

        /// <summary>
        /// 上次跨度样时间
        ///</summary>
        [SugarColumn(ColumnName = "time1", ColumnDescription = "上次跨度样时间")]
        [Description("上次跨度样时间")]
        public DateTime Time1 { get; set; }

        /// <summary>
        /// 本次跨度样时间
        ///</summary>
        [SugarColumn(ColumnName = "time2", ColumnDescription = "本次跨度样时间")]
        [Description("本次跨度样时间")]
        public DateTime Time2 { get; set; }

        #endregion

        #region 公共方法

        /// <summary>
        /// 从设备数据库生成数据
        /// </summary>
        public static List<SpanCheckData> InitFromDeviceDB(SqlSugarClient db, string snCode)
        {
            try
            {
                var dataList = new List<SpanCheckData>();

                List<RawSpanCheckData> rawSpanCheckDataList = db.Queryable<RawSpanCheckData>().ToList();
                foreach(var rawSpanCheckData in rawSpanCheckDataList)
                {
                    try
                    {
                        SpanCheckData spanCheckData = new SpanCheckData();
                        spanCheckData.SNCode = snCode;
                        spanCheckData.Time = DateTime.Parse(rawSpanCheckData.Time);
                        spanCheckData.Value = double.Parse(rawSpanCheckData.Value);
                        spanCheckData.Standard = double.Parse(rawSpanCheckData.Standard);
                        spanCheckData.Err = double.Parse(rawSpanCheckData.Err);
                        spanCheckData.Judge = rawSpanCheckData.Judge;
                        spanCheckData.ErrDif = double.Parse(rawSpanCheckData.ErrDif);
                        spanCheckData.JudgeDif = rawSpanCheckData.JudgeDif;
                        spanCheckData.Time1 = DateTime.Parse(rawSpanCheckData.Time1);
                        spanCheckData.Time2 = DateTime.Parse(rawSpanCheckData.Time2);

                        dataList.Add(spanCheckData);
                    }
                    catch
                    {
                    }
                }

                return dataList;
            }
            catch(Exception ex)
            {
                throw new Exception($"从数据库提取跨度核查数据出错：{ex.Message}");
            }
        }

        #endregion
    }
}