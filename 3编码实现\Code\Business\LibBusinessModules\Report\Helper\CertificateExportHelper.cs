﻿using LibBusinessModules.Report.Config;
using System;
using System.Collections.Generic;

namespace LibBusinessModules.Report.Helper
{
    /// <summary>
    /// 合格证导出帮助类
    /// </summary>
    internal static class CertificateExportHelper
    {
        /// <summary>
        /// 导出合格证文档
        /// </summary>
        /// <param name="calculateNode">报表计算节点</param>
        public static string ExportWordFile(ReportCalculateNode calculateNode)
        {
            var outputPath = ReportPathHelper.GetReportSavePath(calculateNode, "合格证");
            var templateFilePath = ReportPathHelper.GetTemplateFilePath(calculateNode, "合格证");

            // 加载模板文件
            using var doc = ReportDocumentHelper.LoadDocument(templateFilePath);

            // 生成替换信息
            var replacements = PrepareReplacements(calculateNode);

            // 执行基础信息替换
            ReportDocumentHelper.ReplaceAllText(doc, replacements);

            // 保存结果文件
            ReportDocumentHelper.SaveDocument(doc, outputPath);

            // 返回报表文件位置
            return outputPath;
        }

        #region 辅助方法

        /// <summary>
        /// 准备所有需要替换的内容
        /// </summary>
        private static Dictionary<string, string> PrepareReplacements(ReportCalculateNode calculateNode)
        {
            //return new Dictionary<string, string>
            //{
            //    ["{{产品型号}}"] = $"{calculateNode.DeviceConfigInfo.Model}型".PadRight(12),
            //    ["{{产品ID}}"] = calculateNode.SNCode.PadRight(16),
            //    ["{{温度}}"] = FormatTemperature(calculateNode.Temperature),
            //    ["{{相对湿度}}"] = FormatHumidity(calculateNode.Humidity),
            //    ["{{检验依据}}"] = calculateNode.DeviceConfigInfo.InspectionBasis,
            //    ["{{检验时间}}"] = DateTime.Now.ToString("yyyy  年  MM  月  dd  日"),
            //    ["{{检验员}}"] = calculateNode.InspectorName.PadLeft(5).PadRight(7),
            //    ["{{审核员}}"] = calculateNode.AuditorName.PadLeft(5).PadRight(7)
            //};

            return new Dictionary<string, string>
            {
                ["{{产品型号}}"] = $"{calculateNode.DeviceConfigInfo.Model}型",
                ["{{产品ID}}"] = calculateNode.SNCode,
                ["{{温度}}"] = FormatTemperature(calculateNode.Temperature),
                ["{{相对湿度}}"] = FormatHumidity(calculateNode.Humidity),
                ["{{检验依据}}"] = calculateNode.DeviceConfigInfo.InspectionBasis,
                ["{{检验时间}}"] = DateTime.Now.ToString("yyyy  年  MM  月  dd  日"),
                ["{{检验员}}"] = calculateNode.InspectorName,
                ["{{审核员}}"] = calculateNode.AuditorName
            };
        }

        /// <summary>
        /// 格式化温度显示
        /// </summary>
        private static string FormatTemperature(double temperature)
        {
            return double.IsNaN(temperature) ? "温度      ℃" : $"温度 {temperature:F1} ℃";
        }

        /// <summary>
        /// 格式化湿度显示
        /// </summary>
        private static string FormatHumidity(double humidity)
        {
            return double.IsNaN(humidity) ? "相对湿度      %RH" : $"相对湿度 {humidity:F1} %RH";
        }

        #endregion
    }
}