﻿using LibBaseModules.DB;
using LibBusinessModules.DB.Models.Device;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.PC
{
    /// <summary>
    /// 总氮测量数据
    ///</summary>
    [SugarTable("tn_measure_data")]
    [Description("总氮测量数据")]
    public class TnMeasureData : BaseNode
    {
        #region 字段属性

        /// <summary>
        /// 设备序列号
        ///</summary>
        [SugarColumn(ColumnName = "sncode", IsPrimaryKey = true)]
        [Description("设备序列号")]
        public string SNCode { get; set; }

        /// <summary>
        /// 时间
        ///</summary>
        [SugarColumn(ColumnName = "time", IsPrimaryKey = true, SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "数据时间")]
        [Description("数据时间")]
        public DateTime Time { get; set; }

        /// <summary>
        /// 测量值
        ///</summary>
        [SugarColumn(ColumnName = "value", ColumnDescription = "浓度")]
        [Description("测量值")]
        public double Value { get; set; }

        /// <summary>
        /// 量程
        ///</summary>
        [SugarColumn(ColumnName = "range", ColumnDescription = "量程")]
        [Description("量程")]
        public double Range { get; set; }

        /// <summary>
        /// 数据标识
        ///</summary>
        [SugarColumn(ColumnName = "flag", ColumnDescription = "数据标识")]
        [Description("数据标识")]
        public string Flag { get; set; }

        /// <summary>
        /// 数据类型
        ///</summary>
        [SugarColumn(ColumnName = "type", ColumnDescription = "数据类型")]
        [Description("数据类型")]
        public string Type { get; set; }

        /// <summary>
        /// 吸光度
        ///</summary>
        [SugarColumn(ColumnName = "abs", ColumnDescription = "吸光度")]
        [Description("吸光度")]
        public double Abs { get; set; }

        /// <summary>
        /// 吸光度220
        ///</summary>
        [SugarColumn(ColumnName = "abs1", ColumnDescription = "吸光度220")]
        [Description("吸光度220")]
        public double Abs1 { get; set; }

        /// <summary>
        /// 吸光度275
        ///</summary>
        [SugarColumn(ColumnName = "abs2", ColumnDescription = "吸光度275")]
        [Description("吸光度275")]
        public double Abs2 { get; set; }

        /// <summary>
        /// V1_220
        ///</summary>
        [SugarColumn(ColumnName = "signal2201", ColumnDescription = "V1_220")]
        [Description("V1_220")]
        public double Signal2201 { get; set; }

        /// <summary>
        /// V1_275
        ///</summary>
        [SugarColumn(ColumnName = "signal2751", ColumnDescription = "V1_275")]
        [Description("V1_275")]
        public double Signal2751 { get; set; }

        /// <summary>
        /// V2_220
        ///</summary>
        [SugarColumn(ColumnName = "signal2202", ColumnDescription = "V2_220")]
        [Description("V2_220")]
        public double Signal2202 { get; set; }

        /// <summary>
        /// V2_275
        ///</summary>
        [SugarColumn(ColumnName = "signal2752", ColumnDescription = "V2_275")]
        [Description("V2_275")]
        public double Signal2752 { get; set; }

        /// <summary>
        /// 测量类型
        ///</summary>
        [SugarColumn(ColumnName = "type1", ColumnDescription = "测量类型")]
        [Description("测量类型")]
        public string Type1 { get; set; }

        #endregion

        #region 公共方法

        /// <summary>
        /// 从设备数据库生成数据
        /// </summary>
        public static List<TnMeasureData> InitFromDeviceDB(SqlSugarClient db, string snCode)
        {
            try
            {
                List<TnMeasureData> dataList = new List<TnMeasureData>();

                List<TnRawMeasuredata> rawMeasureDataList = db.Queryable<TnRawMeasuredata>().ToList();
                foreach(TnRawMeasuredata rawMeasureData in rawMeasureDataList)
                {
                    try
                    {
                        TnMeasureData curveData = new TnMeasureData();
                        curveData.SNCode = snCode;
                        curveData.Time = DateTime.Parse(rawMeasureData.Time);
                        curveData.Value = double.Parse(rawMeasureData.Value);
                        curveData.Range = double.Parse(rawMeasureData.Range);
                        curveData.Flag = rawMeasureData.Flag;
                        curveData.Type1 = rawMeasureData.Type1;
                        curveData.Type = rawMeasureData.Type;
                        curveData.Abs = double.Parse(rawMeasureData.Abs);
                        curveData.Abs1 = double.Parse(rawMeasureData.Abs1);
                        curveData.Abs2 = double.Parse(rawMeasureData.Abs2);
                        curveData.Signal2201 = double.Parse(rawMeasureData.Signal2201);
                        curveData.Signal2751 = double.Parse(rawMeasureData.Signal2751);
                        curveData.Signal2202 = double.Parse(rawMeasureData.Signal2202);
                        curveData.Signal2752 = double.Parse(rawMeasureData.Signal2752);

                        dataList.Add(curveData);
                    }
                    catch
                    {
                    }
                }

                return dataList;
            }
            catch(Exception ex)
            {
                throw new Exception($"从数据库提取总氮测量数据出错：{ex.Message}");
            }
        }

        #endregion
    }
}