﻿using System;
using System.Text;
using System.Threading;
using LibBaseModules.Helper;
using Parity = LibBaseModules.Helper.WinApiUtil.CommDataType.Parity;
using StopBits = LibBaseModules.Helper.WinApiUtil.CommDataType.StopBits;

namespace LibBaseModules.Communication
{
    /// <summary>
    /// 通信通道
    /// </summary>
    public class ComPipe
    {
        #region 配置相关变量

        /// <summary>
        /// 串口号
        /// </summary>
        private string _portName;

        /// <summary>
        /// 波特率
        /// </summary>
        private int _baudRate;

        /// <summary>
        /// 校验位
        /// </summary>
        private Parity _parity = Parity.None;

        /// <summary>
        /// 停止位
        /// </summary>
        private StopBits _stopBits = StopBits.one;

        /// <summary>
        /// 数据位
        /// </summary>
        private int _dataBits = 8;

        /// <summary>
        /// 重发次数
        /// </summary>
        private int _tryTimes = 2;

        /// <summary>
        /// 超时时间
        /// </summary>
        private int _timeout = 2000;

        #endregion

        #region 字段属性

        /// <summary>
        /// 当前通信是否正常
        /// </summary>
        public bool IsConnect => _comPort.IsConnected;

        /// <summary>
        /// 通信组件
        /// </summary>
        private readonly ComBus _comPort = new ComBus();

        /// <summary>
        /// 发送同步锁
        /// </summary>
        private readonly object _lockObj = new object();

        #endregion

        #region 公共方法

        /// <summary>
        /// 初始化通信参数
        /// </summary>
        /// <param name="portName">串口名</param>
        /// <param name="baudRate">波特率</param>
        /// <param name="dataBits">数据位</param>
        /// <param name="stopBits">停止位</param>
        /// <param name="parity">校验位</param>
        /// <param name="tryTimes">重发次数</param>
        /// <param name="timeout">超时时间</param>
        public void InitParam(string portName, int baudRate,  Parity parity, StopBits stopBits, int dataBits, int tryTimes, int timeout)
        {
            lock(this)
            {
                _portName = portName;
                _baudRate = baudRate;
                _parity = parity;
                _stopBits = stopBits;
                _dataBits = dataBits;
                _tryTimes = tryTimes;
                _timeout = timeout;
            }
        }

        /// <summary>
        /// 返回通信状态描述信息
        /// </summary>
        /// <returns></returns>
        public string GetStatusInfo()
        {
            return $"串口号：{_portName}，波特率：{_baudRate}。";
        }


        public void Open()
        {
            try
            {
                lock(this)
                {
                    if(IsConnect)
                    {
                        Close();
                    }
                    Connect();
                }
            }
            catch(Exception e)
            {
                string info = $"开启通信通道失败：{e.Message}";
                LogUtil.GetInstance().LogWrite(info, MsgLevel.Error);
            }
        }

        public void Close()
        {
            try
            {
                lock(this)
                {
                    DisConnect();
                }
            }
            catch(Exception e)
            {
                string info = $"关闭通信通道失败：{e.Message}";
                LogUtil.GetInstance().LogWrite(info, MsgLevel.Error);
            }
        }

        /// <summary>
        /// 发送数据(bytes)
        /// </summary>
        /// <param name="data"></param>
        public byte[] SendData(byte[] data)
        {
            lock(_lockObj)
            {
                // 设备启用时，自动打开串口
                if(!IsConnect)
                {
                    Connect();
                    Thread.Sleep(100);
                }

                return _comPort.SendData(data);
            }
        }

        /// <summary>
        /// 发送数据(string)
        /// </summary>
        /// <param name="dataStr"></param>
        public string SendData(string dataStr)
        {
            var returnData = SendData(Encoding.ASCII.GetBytes(dataStr));
            return returnData == null ? null : Encoding.ASCII.GetString(returnData);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 连接设备
        /// </summary>
        private void Connect()
        {
            if(!_comPort.IsConnected)
            {
                _comPort.Open(_portName, _baudRate, _dataBits, _stopBits, _parity, _tryTimes, _timeout);
            }
        }

        /// <summary>
        /// 断开设备
        /// </summary>
        private void DisConnect()
        {
            if(_comPort.IsConnected)
            {
                _comPort.Close();
            }
        }

        #endregion
    }
}