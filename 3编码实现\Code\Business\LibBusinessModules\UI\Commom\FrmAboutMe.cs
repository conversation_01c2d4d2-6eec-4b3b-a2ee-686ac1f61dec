﻿using LibBusinessModules.Helper;
using Sunny.UI;

namespace LibBusinessModules.UI.Commom
{
    partial class FrmAboutMe : UIForm
    {
        public FrmAboutMe()
        {
            InitializeComponent();
            Text = $@"关于 {AppInfoHelper.AssemblyProduct}";
            label1.Text = $@"产品名称: {AppInfoHelper.AssemblyProduct}";
            label2.Text = $@"版本: {AppInfoHelper.AssemblyVersion}";
            label3.Text = $@"公司: {AppInfoHelper.AssemblyCompany}";
            label4.Text = $@"更新时间: {System.IO.File.GetLastWriteTime(GetType().Assembly.Location):yyyy-MM-dd}";
            textBoxDescription.Text = AppInfoHelper.AssemblyDescription;
        }
    }
}
