﻿namespace LibBusinessModules.UI.Com
{
    partial class UC_ComTest
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.btn_ClearSend = new Sunny.UI.UIButton();
            this.gbSerialConfig = new Sunny.UI.UIGroupBox();
            this.btn_Connect = new Sunny.UI.UIButton();
            this.cmb_Parity = new Sunny.UI.UIComboBox();
            this.lbl_Parity = new Sunny.UI.UILabel();
            this.cmb_DataBits = new Sunny.UI.UIComboBox();
            this.lbl_DataBits = new Sunny.UI.UILabel();
            this.cmb_StopBits = new Sunny.UI.UIComboBox();
            this.lbl_StopBits = new Sunny.UI.UILabel();
            this.cmb_BaudRate = new Sunny.UI.UIComboBox();
            this.lbl_BaudRate = new Sunny.UI.UILabel();
            this.cmb_PortName = new Sunny.UI.UIComboBox();
            this.lbl_PortName = new Sunny.UI.UILabel();
            this.gbSendData = new Sunny.UI.UIGroupBox();
            this.btn_Send = new Sunny.UI.UIButton();
            this.gbReceiveData = new Sunny.UI.UIGroupBox();
            this.btn_ClearReceive = new Sunny.UI.UIButton();
            this.txt_ReceiveData = new Sunny.UI.UIRichTextBox();
            this.uiLight = new Sunny.UI.UILight();
            this.txt_SendData = new Sunny.UI.UIRichTextBox();
            this.uiPanel1 = new Sunny.UI.UIPanel();
            this.pnlSerialConfig = new Sunny.UI.UIPanel();
            this.gbSerialConfig.SuspendLayout();
            this.gbSendData.SuspendLayout();
            this.gbReceiveData.SuspendLayout();
            this.uiPanel1.SuspendLayout();
            this.pnlSerialConfig.SuspendLayout();
            this.SuspendLayout();
            // 
            // btn_ClearSend
            // 
            this.btn_ClearSend.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btn_ClearSend.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.btn_ClearSend.Location = new System.Drawing.Point(24, 15);
            this.btn_ClearSend.MinimumSize = new System.Drawing.Size(1, 1);
            this.btn_ClearSend.Name = "btn_ClearSend";
            this.btn_ClearSend.Size = new System.Drawing.Size(59, 25);
            this.btn_ClearSend.TabIndex = 3;
            this.btn_ClearSend.Text = "清空";
            this.btn_ClearSend.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btn_ClearSend.Click += new System.EventHandler(this.btn_ClearSend_Click);
            // 
            // gbSerialConfig
            // 
            this.gbSerialConfig.Controls.Add(this.uiLight);
            this.gbSerialConfig.Controls.Add(this.btn_Connect);
            this.gbSerialConfig.Controls.Add(this.cmb_Parity);
            this.gbSerialConfig.Controls.Add(this.lbl_Parity);
            this.gbSerialConfig.Controls.Add(this.cmb_DataBits);
            this.gbSerialConfig.Controls.Add(this.lbl_DataBits);
            this.gbSerialConfig.Controls.Add(this.cmb_StopBits);
            this.gbSerialConfig.Controls.Add(this.lbl_StopBits);
            this.gbSerialConfig.Controls.Add(this.cmb_BaudRate);
            this.gbSerialConfig.Controls.Add(this.lbl_BaudRate);
            this.gbSerialConfig.Controls.Add(this.cmb_PortName);
            this.gbSerialConfig.Controls.Add(this.lbl_PortName);
            this.gbSerialConfig.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbSerialConfig.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.gbSerialConfig.Location = new System.Drawing.Point(0, 0);
            this.gbSerialConfig.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbSerialConfig.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbSerialConfig.Name = "gbSerialConfig";
            this.gbSerialConfig.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.gbSerialConfig.Size = new System.Drawing.Size(196, 458);
            this.gbSerialConfig.TabIndex = 0;
            this.gbSerialConfig.Text = "通信控制";
            this.gbSerialConfig.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btn_Connect
            // 
            this.btn_Connect.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btn_Connect.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btn_Connect.Location = new System.Drawing.Point(70, 304);
            this.btn_Connect.MinimumSize = new System.Drawing.Size(1, 1);
            this.btn_Connect.Name = "btn_Connect";
            this.btn_Connect.Size = new System.Drawing.Size(112, 29);
            this.btn_Connect.TabIndex = 5;
            this.btn_Connect.Text = "打开串口";
            this.btn_Connect.TipsFont = new System.Drawing.Font("微软雅黑", 9F);
            this.btn_Connect.Click += new System.EventHandler(this.btn_Connect_Click);
            // 
            // cmb_Parity
            // 
            this.cmb_Parity.DataSource = null;
            this.cmb_Parity.DropDownAutoWidth = true;
            this.cmb_Parity.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmb_Parity.FillColor = System.Drawing.Color.White;
            this.cmb_Parity.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.cmb_Parity.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmb_Parity.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmb_Parity.Location = new System.Drawing.Point(70, 238);
            this.cmb_Parity.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmb_Parity.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmb_Parity.Name = "cmb_Parity";
            this.cmb_Parity.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmb_Parity.Size = new System.Drawing.Size(112, 29);
            this.cmb_Parity.SymbolSize = 24;
            this.cmb_Parity.TabIndex = 4;
            this.cmb_Parity.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmb_Parity.Watermark = "";
            // 
            // lbl_Parity
            // 
            this.lbl_Parity.AutoSize = true;
            this.lbl_Parity.BackColor = System.Drawing.Color.Transparent;
            this.lbl_Parity.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lbl_Parity.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lbl_Parity.Location = new System.Drawing.Point(12, 242);
            this.lbl_Parity.Name = "lbl_Parity";
            this.lbl_Parity.Size = new System.Drawing.Size(58, 21);
            this.lbl_Parity.TabIndex = 9;
            this.lbl_Parity.Text = "校验位";
            this.lbl_Parity.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // cmb_DataBits
            // 
            this.cmb_DataBits.DataSource = null;
            this.cmb_DataBits.DropDownAutoWidth = true;
            this.cmb_DataBits.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmb_DataBits.FillColor = System.Drawing.Color.White;
            this.cmb_DataBits.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.cmb_DataBits.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmb_DataBits.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmb_DataBits.Location = new System.Drawing.Point(70, 144);
            this.cmb_DataBits.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmb_DataBits.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmb_DataBits.Name = "cmb_DataBits";
            this.cmb_DataBits.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmb_DataBits.Size = new System.Drawing.Size(112, 29);
            this.cmb_DataBits.SymbolSize = 24;
            this.cmb_DataBits.TabIndex = 2;
            this.cmb_DataBits.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmb_DataBits.Watermark = "";
            // 
            // lbl_DataBits
            // 
            this.lbl_DataBits.AutoSize = true;
            this.lbl_DataBits.BackColor = System.Drawing.Color.Transparent;
            this.lbl_DataBits.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lbl_DataBits.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lbl_DataBits.Location = new System.Drawing.Point(12, 148);
            this.lbl_DataBits.Name = "lbl_DataBits";
            this.lbl_DataBits.Size = new System.Drawing.Size(58, 21);
            this.lbl_DataBits.TabIndex = 7;
            this.lbl_DataBits.Text = "数据位";
            this.lbl_DataBits.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // cmb_StopBits
            // 
            this.cmb_StopBits.DataSource = null;
            this.cmb_StopBits.DropDownAutoWidth = true;
            this.cmb_StopBits.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmb_StopBits.FillColor = System.Drawing.Color.White;
            this.cmb_StopBits.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.cmb_StopBits.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmb_StopBits.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmb_StopBits.Location = new System.Drawing.Point(70, 191);
            this.cmb_StopBits.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmb_StopBits.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmb_StopBits.Name = "cmb_StopBits";
            this.cmb_StopBits.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmb_StopBits.Size = new System.Drawing.Size(112, 29);
            this.cmb_StopBits.SymbolSize = 24;
            this.cmb_StopBits.TabIndex = 3;
            this.cmb_StopBits.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmb_StopBits.Watermark = "";
            // 
            // lbl_StopBits
            // 
            this.lbl_StopBits.AutoSize = true;
            this.lbl_StopBits.BackColor = System.Drawing.Color.Transparent;
            this.lbl_StopBits.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lbl_StopBits.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lbl_StopBits.Location = new System.Drawing.Point(12, 195);
            this.lbl_StopBits.Name = "lbl_StopBits";
            this.lbl_StopBits.Size = new System.Drawing.Size(58, 21);
            this.lbl_StopBits.TabIndex = 5;
            this.lbl_StopBits.Text = "停止位";
            this.lbl_StopBits.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // cmb_BaudRate
            // 
            this.cmb_BaudRate.DataSource = null;
            this.cmb_BaudRate.DropDownAutoWidth = true;
            this.cmb_BaudRate.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmb_BaudRate.FillColor = System.Drawing.Color.White;
            this.cmb_BaudRate.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.cmb_BaudRate.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmb_BaudRate.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmb_BaudRate.Location = new System.Drawing.Point(70, 97);
            this.cmb_BaudRate.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmb_BaudRate.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmb_BaudRate.Name = "cmb_BaudRate";
            this.cmb_BaudRate.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmb_BaudRate.Size = new System.Drawing.Size(112, 29);
            this.cmb_BaudRate.SymbolSize = 24;
            this.cmb_BaudRate.TabIndex = 1;
            this.cmb_BaudRate.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmb_BaudRate.Watermark = "";
            // 
            // lbl_BaudRate
            // 
            this.lbl_BaudRate.AutoSize = true;
            this.lbl_BaudRate.BackColor = System.Drawing.Color.Transparent;
            this.lbl_BaudRate.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lbl_BaudRate.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lbl_BaudRate.Location = new System.Drawing.Point(12, 101);
            this.lbl_BaudRate.Name = "lbl_BaudRate";
            this.lbl_BaudRate.Size = new System.Drawing.Size(58, 21);
            this.lbl_BaudRate.TabIndex = 3;
            this.lbl_BaudRate.Text = "波特率";
            this.lbl_BaudRate.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // cmb_PortName
            // 
            this.cmb_PortName.DataSource = null;
            this.cmb_PortName.DropDownAutoWidth = true;
            this.cmb_PortName.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmb_PortName.FillColor = System.Drawing.Color.White;
            this.cmb_PortName.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.cmb_PortName.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmb_PortName.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmb_PortName.Location = new System.Drawing.Point(70, 50);
            this.cmb_PortName.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmb_PortName.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmb_PortName.Name = "cmb_PortName";
            this.cmb_PortName.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmb_PortName.Size = new System.Drawing.Size(112, 29);
            this.cmb_PortName.SymbolSize = 24;
            this.cmb_PortName.TabIndex = 0;
            this.cmb_PortName.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmb_PortName.Watermark = "";
            // 
            // lbl_PortName
            // 
            this.lbl_PortName.AutoSize = true;
            this.lbl_PortName.BackColor = System.Drawing.Color.Transparent;
            this.lbl_PortName.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lbl_PortName.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lbl_PortName.Location = new System.Drawing.Point(12, 54);
            this.lbl_PortName.Name = "lbl_PortName";
            this.lbl_PortName.Size = new System.Drawing.Size(58, 21);
            this.lbl_PortName.TabIndex = 1;
            this.lbl_PortName.Text = "串口号";
            this.lbl_PortName.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // gbSendData
            // 
            this.gbSendData.Controls.Add(this.txt_SendData);
            this.gbSendData.Controls.Add(this.uiPanel1);
            this.gbSendData.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.gbSendData.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.gbSendData.Location = new System.Drawing.Point(1, 459);
            this.gbSendData.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbSendData.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbSendData.Name = "gbSendData";
            this.gbSendData.Padding = new System.Windows.Forms.Padding(0, 32, 5, 5);
            this.gbSendData.Size = new System.Drawing.Size(896, 109);
            this.gbSendData.TabIndex = 1;
            this.gbSendData.Text = "发送区域";
            this.gbSendData.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btn_Send
            // 
            this.btn_Send.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btn_Send.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.btn_Send.Location = new System.Drawing.Point(107, 15);
            this.btn_Send.MinimumSize = new System.Drawing.Size(1, 1);
            this.btn_Send.Name = "btn_Send";
            this.btn_Send.Size = new System.Drawing.Size(59, 25);
            this.btn_Send.TabIndex = 2;
            this.btn_Send.Text = "发送";
            this.btn_Send.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btn_Send.Click += new System.EventHandler(this.btn_Send_Click);
            // 
            // gbReceiveData
            // 
            this.gbReceiveData.Controls.Add(this.btn_ClearReceive);
            this.gbReceiveData.Controls.Add(this.txt_ReceiveData);
            this.gbReceiveData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbReceiveData.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.gbReceiveData.Location = new System.Drawing.Point(198, 1);
            this.gbReceiveData.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbReceiveData.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbReceiveData.Name = "gbReceiveData";
            this.gbReceiveData.Padding = new System.Windows.Forms.Padding(1, 32, 1, 1);
            this.gbReceiveData.RectSides = ((System.Windows.Forms.ToolStripStatusLabelBorderSides)(((System.Windows.Forms.ToolStripStatusLabelBorderSides.Top | System.Windows.Forms.ToolStripStatusLabelBorderSides.Right) 
            | System.Windows.Forms.ToolStripStatusLabelBorderSides.Bottom)));
            this.gbReceiveData.Size = new System.Drawing.Size(699, 458);
            this.gbReceiveData.TabIndex = 2;
            this.gbReceiveData.Text = "收发日志";
            this.gbReceiveData.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btn_ClearReceive
            // 
            this.btn_ClearReceive.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btn_ClearReceive.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.btn_ClearReceive.Location = new System.Drawing.Point(103, 3);
            this.btn_ClearReceive.MinimumSize = new System.Drawing.Size(1, 1);
            this.btn_ClearReceive.Name = "btn_ClearReceive";
            this.btn_ClearReceive.Size = new System.Drawing.Size(50, 25);
            this.btn_ClearReceive.TabIndex = 2;
            this.btn_ClearReceive.Text = "清空";
            this.btn_ClearReceive.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btn_ClearReceive.Click += new System.EventHandler(this.btn_ClearReceive_Click);
            // 
            // txt_ReceiveData
            // 
            this.txt_ReceiveData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txt_ReceiveData.FillColor = System.Drawing.Color.White;
            this.txt_ReceiveData.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.txt_ReceiveData.Location = new System.Drawing.Point(1, 32);
            this.txt_ReceiveData.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txt_ReceiveData.MinimumSize = new System.Drawing.Size(1, 1);
            this.txt_ReceiveData.Name = "txt_ReceiveData";
            this.txt_ReceiveData.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.txt_ReceiveData.ReadOnly = true;
            this.txt_ReceiveData.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.txt_ReceiveData.ShowText = false;
            this.txt_ReceiveData.Size = new System.Drawing.Size(697, 425);
            this.txt_ReceiveData.TabIndex = 1;
            this.txt_ReceiveData.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uiLight
            // 
            this.uiLight.CenterColor = System.Drawing.Color.Red;
            this.uiLight.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLight.Location = new System.Drawing.Point(19, 301);
            this.uiLight.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiLight.Name = "uiLight";
            this.uiLight.OffCenterColor = System.Drawing.Color.Black;
            this.uiLight.OffColor = System.Drawing.Color.Black;
            this.uiLight.OnCenterColor = System.Drawing.Color.Red;
            this.uiLight.OnColor = System.Drawing.Color.Red;
            this.uiLight.Radius = 35;
            this.uiLight.Size = new System.Drawing.Size(35, 35);
            this.uiLight.State = Sunny.UI.UILightState.Off;
            this.uiLight.TabIndex = 10;
            // 
            // txt_SendData
            // 
            this.txt_SendData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txt_SendData.FillColor = System.Drawing.Color.White;
            this.txt_SendData.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.txt_SendData.Location = new System.Drawing.Point(197, 32);
            this.txt_SendData.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txt_SendData.MinimumSize = new System.Drawing.Size(1, 1);
            this.txt_SendData.Name = "txt_SendData";
            this.txt_SendData.Padding = new System.Windows.Forms.Padding(2);
            this.txt_SendData.ShowText = false;
            this.txt_SendData.Size = new System.Drawing.Size(694, 72);
            this.txt_SendData.TabIndex = 4;
            this.txt_SendData.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            this.txt_SendData.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txt_SendData_KeyPress);
            // 
            // uiPanel1
            // 
            this.uiPanel1.Controls.Add(this.btn_ClearSend);
            this.uiPanel1.Controls.Add(this.btn_Send);
            this.uiPanel1.Dock = System.Windows.Forms.DockStyle.Left;
            this.uiPanel1.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiPanel1.Location = new System.Drawing.Point(0, 32);
            this.uiPanel1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiPanel1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiPanel1.Name = "uiPanel1";
            this.uiPanel1.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uiPanel1.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uiPanel1.Size = new System.Drawing.Size(197, 72);
            this.uiPanel1.TabIndex = 5;
            this.uiPanel1.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // pnlSerialConfig
            // 
            this.pnlSerialConfig.Controls.Add(this.gbSerialConfig);
            this.pnlSerialConfig.Dock = System.Windows.Forms.DockStyle.Left;
            this.pnlSerialConfig.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlSerialConfig.Location = new System.Drawing.Point(1, 1);
            this.pnlSerialConfig.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlSerialConfig.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlSerialConfig.Name = "pnlSerialConfig";
            this.pnlSerialConfig.Padding = new System.Windows.Forms.Padding(0, 0, 1, 0);
            this.pnlSerialConfig.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.pnlSerialConfig.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.pnlSerialConfig.Size = new System.Drawing.Size(197, 458);
            this.pnlSerialConfig.TabIndex = 11;
            this.pnlSerialConfig.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // UC_ComTest
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.gbReceiveData);
            this.Controls.Add(this.pnlSerialConfig);
            this.Controls.Add(this.gbSendData);
            this.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "UC_ComTest";
            this.Padding = new System.Windows.Forms.Padding(1);
            this.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.Size = new System.Drawing.Size(898, 569);
            this.Text = "通信测试";
            this.gbSerialConfig.ResumeLayout(false);
            this.gbSerialConfig.PerformLayout();
            this.gbSendData.ResumeLayout(false);
            this.gbReceiveData.ResumeLayout(false);
            this.uiPanel1.ResumeLayout(false);
            this.pnlSerialConfig.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private Sunny.UI.UIGroupBox gbSerialConfig;
        private Sunny.UI.UIComboBox cmb_Parity;
        private Sunny.UI.UILabel lbl_Parity;
        private Sunny.UI.UIComboBox cmb_DataBits;
        private Sunny.UI.UILabel lbl_DataBits;
        private Sunny.UI.UIComboBox cmb_StopBits;
        private Sunny.UI.UILabel lbl_StopBits;
        private Sunny.UI.UIComboBox cmb_BaudRate;
        private Sunny.UI.UILabel lbl_BaudRate;
        private Sunny.UI.UIComboBox cmb_PortName;
        private Sunny.UI.UILabel lbl_PortName;
        private Sunny.UI.UIButton btn_Connect;
        private Sunny.UI.UIGroupBox gbSendData;
        private Sunny.UI.UIButton btn_ClearSend;
        private Sunny.UI.UIButton btn_Send;
        private Sunny.UI.UIGroupBox gbReceiveData;
        private Sunny.UI.UIButton btn_ClearReceive;
        private Sunny.UI.UIRichTextBox txt_ReceiveData;
        private Sunny.UI.UILight uiLight;
        private Sunny.UI.UIRichTextBox txt_SendData;
        private Sunny.UI.UIPanel uiPanel1;
        private Sunny.UI.UIPanel pnlSerialConfig;
    }
}
