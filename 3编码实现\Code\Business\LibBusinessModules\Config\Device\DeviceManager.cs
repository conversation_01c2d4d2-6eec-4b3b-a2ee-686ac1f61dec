﻿using LibBaseModules.Json;
using System;
using System.Collections.Generic;
using System.Linq;

namespace LibBusinessModules.Config
{
    /// <summary>
    /// 设备因子配置管理类
    /// </summary>
    public class DeviceManager : BaseJsonNode
    {
        #region 字段属性

        /// <summary>
        /// 设备因子配置列表
        /// </summary>
        public List<DeviceConfig> DeviceList = new List<DeviceConfig>();

        #endregion

        #region 单例

        private static readonly object SyncObj = new object();
        private static DeviceManager _instance = null;
        public static DeviceManager GetInstance()
        {
            lock(SyncObj)
            {
                if(_instance == null)
                {
                    _instance = new DeviceManager();
                }
            }
            return _instance;
        }

        private DeviceManager()
        {
            loadJson();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 返回按ID识别码排序的设备列表
        /// </summary>
        /// <returns></returns>
        public List<DeviceConfig> GetDeviceList()
        {
            return DeviceList.OrderBy(x => x.IdCode).ToList();
        }

        /// <summary>
        /// 根据设备序列化获取设备配置
        /// </summary>
        /// <param name="snCode"></param>
        /// <returns></returns>
        public DeviceConfig GetDeviceConfig(string snCode)
        {
            try
            {
                if(string.IsNullOrEmpty(snCode) || snCode.Length < 6)
                {
                    throw new Exception($"序列号应为长度至少为6的字符，当前序列号[{snCode}]格式不合法！");
                }

                return DeviceList.FirstOrDefault(x => x.IdCode.ToLower() == snCode.Substring(0, 6).ToLower());
            }
            catch(Exception e)
            {
                throw new Exception($"获取设备配置信息失败：{e.Message}");
            }
        }

        /// <summary>
        /// 重新从本地加载数据
        /// </summary>
        public void ReLoad()
        {
            _instance = null;
        }

        #endregion
    }
}