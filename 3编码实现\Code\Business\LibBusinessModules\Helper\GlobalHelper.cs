﻿using LibBusinessModules.Config;

namespace LibBusinessModules.Helper
{
    /// <summary>
    /// 公共帮助类
    /// </summary>
    internal static class GlobalHelper
    {
        #region 全局标志量

        /// <summary>
        /// 强制关闭标志
        /// </summary>
        internal static bool IsForseClose { get; set; }

        /// <summary>
        /// 用户登录标志
        /// </summary>
        internal static bool IsUserLogin { get; set; }

        #endregion

        #region 全局对象

        /// <summary>
        /// 当前系统配置
        /// </summary>
        internal static SystemConfig CurrentSystemConfig;

        #endregion

        #region 公共方法

        /// <summary>
        /// 初始化系统配置
        /// </summary>
        public static void InitializerConfig()
        {
            CurrentSystemConfig = SystemConfig.GetInstance();
        }

        #endregion
    }
}