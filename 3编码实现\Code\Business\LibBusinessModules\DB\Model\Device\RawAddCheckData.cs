﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 加标回收数据
    ///</summary>
    [SugarTable("addcheckData")]
    [Description("加标回收数据")]
    public class RawAddCheckData
    {
        /// <summary>
        /// 时间
        ///</summary>
        [SugarColumn(ColumnName = "time")]
        [Description("时间")]
        public string? Time { get; set; }

        /// <summary>
        /// 加标前测量值
        ///</summary>
        [SugarColumn(ColumnName = "value1")]
        [Description("加标前测量值")]
        public string? Value1 { get; set; }

        /// <summary>
        /// 加标后测量值
        ///</summary>
        [SugarColumn(ColumnName = "value2")]
        [Description("加标后测量值")]
        public string? Value2 { get; set; }

        /// <summary>
        /// 加标体积
        ///</summary>
        [SugarColumn(ColumnName = "addVolume")]
        [Description("加标体积")]
        public string? AddVolume { get; set; }

        /// <summary>
        /// 标准值
        ///</summary>
        [SugarColumn(ColumnName = "standard")]
        [Description("标准值")]
        public string? Standard { get; set; }

        /// <summary>
        /// 回收率
        ///</summary>
        [SugarColumn(ColumnName = "rate")]
        [Description("回收率")]
        public string? Rate { get; set; }

        /// <summary>
        /// 回收判定
        ///</summary>
        [SugarColumn(ColumnName = "judge")]
        [Description("回收判定")]
        public string? Judge { get; set; }

        /// <summary>
        /// 加标前采样时间
        ///</summary>
        [SugarColumn(ColumnName = "time1")]
        [Description("加标前采样时间")]
        public string? Time1 { get; set; }

        /// <summary>
        /// 加标后采样时间
        ///</summary>
        [SugarColumn(ColumnName = "time2")]
        [Description("加标后采样时间")]
        public string? Time2 { get; set; }
    }
}