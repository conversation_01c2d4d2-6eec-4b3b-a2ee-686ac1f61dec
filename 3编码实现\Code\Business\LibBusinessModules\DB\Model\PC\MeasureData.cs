﻿using LibBaseModules.DB;
using LibBusinessModules.DB.Models.Device;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.PC
{
    /// <summary>
    /// 常规测量数据
    ///</summary>
    [SugarTable("measure_data")]
    [Description("常规测量数据")]
    public class MeasureData : BaseNode
    {
        #region 字段属性

        /// <summary>
        /// 设备序列号
        ///</summary>
        [SugarColumn(ColumnName = "sncode", IsPrimaryKey = true)]
        [Description("设备序列号")]
        public string SNCode { get; set; }

        /// <summary>
        /// 数据时间
        ///</summary>
        [SugarColumn(ColumnName = "time", IsPrimaryKey = true, SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "数据时间")]
        [Description("数据时间")]
        public DateTime Time { get; set; }
        /// <summary>
        /// 测量值
        ///</summary>
        [SugarColumn(ColumnName = "value", ColumnDescription = "浓度")]
        [Description("测量值")]
        public double Value { get; set; }

        /// <summary>
        /// 量程
        ///</summary>
        [SugarColumn(ColumnName = "range", ColumnDescription = "量程")]
        [Description("量程")]
        public double Range { get; set; }

        /// <summary>
        /// 数据标识
        ///</summary>
        [SugarColumn(ColumnName = "flag", ColumnDescription = "数据标识")]
        [Description("数据标识")]
        public string Flag { get; set; }

        /// <summary>
        /// 数据类型
        ///</summary>
        [SugarColumn(ColumnName = "type", ColumnDescription = "数据类型")]
        [Description("数据类型")]
        public string Type { get; set; }

        /// <summary>
        /// 吸光度
        ///</summary>
        [SugarColumn(ColumnName = "abs", ColumnDescription = "吸光度")]
        [Description("吸光度")]
        public double Abs { get; set; }

        /// <summary>
        /// 吸光度1
        ///</summary>
        [SugarColumn(ColumnName = "abs1", ColumnDescription = "吸光度1")]
        [Description("吸光度1")]
        public double Abs1 { get; set; }

        /// <summary>
        /// 吸光度2
        ///</summary>
        [SugarColumn(ColumnName = "abs2", ColumnDescription = "吸光度2")]
        [Description("吸光度2")]
        public double Abs2 { get; set; }

        /// <summary>
        /// 吸光度3
        ///</summary>
        [SugarColumn(ColumnName = "abs3", ColumnDescription = "吸光度3")]
        [Description("吸光度3")]
        public double Abs3 { get; set; }

        /// <summary>
        /// V1检测
        ///</summary>
        [SugarColumn(ColumnName = "signalMain1", ColumnDescription = "V1检测")]
        [Description("V1检测")]
        public double SignalMain1 { get; set; }

        /// <summary>
        /// V1参比
        ///</summary>
        [SugarColumn(ColumnName = "signalRef1", ColumnDescription = "V1参比")]
        [Description("V1参比")]
        public double SignalRef1 { get; set; }

        /// <summary>
        /// V2检测
        ///</summary>
        [SugarColumn(ColumnName = "signalMain2", ColumnDescription = "V2检测")]
        [Description("V2检测")]
        public double SignalMain2 { get; set; }

        /// <summary>
        /// V2参比
        ///</summary>
        [SugarColumn(ColumnName = "signalRef2", ColumnDescription = "V2参比")]
        [Description("V2参比")]
        public double SignalRef2 { get; set; }

        /// <summary>
        /// V3检测
        ///</summary>
        [SugarColumn(ColumnName = "signalMain3", ColumnDescription = "V3检测")]
        [Description("V3检测")]
        public double SignalMain3 { get; set; }

        /// <summary>
        /// V3参比
        ///</summary>
        [SugarColumn(ColumnName = "signalRef3", ColumnDescription = "V3参比")]
        [Description("V3参比")]
        public double SignalRef3 { get; set; }

        /// <summary>
        /// 测量类型
        ///</summary>
        [SugarColumn(ColumnName = "type1", ColumnDescription = "测量类型")]
        [Description("测量类型")]
        public string Type1 { get; set; }

        #endregion

        #region 公共方法

        /// <summary>
        /// 从设备数据库生成数据
        /// </summary>
        public static List<MeasureData> InitFromDeviceDB(SqlSugarClient db, string snCode)
        {
            try
            {
                List<MeasureData> dataList = new List<MeasureData>();

                List<RawMeasureData> rawMeasureDataList = db.Queryable<RawMeasureData>().ToList();
                foreach(RawMeasureData rawMeasureData in rawMeasureDataList)
                {
                    try
                    {
                        MeasureData curveData = new MeasureData();
                        curveData.SNCode = snCode;
                        curveData.Time = DateTime.Parse(rawMeasureData.Time);
                        curveData.Value = double.Parse(rawMeasureData.Value);
                        curveData.Range = double.Parse(rawMeasureData.Range);
                        curveData.Flag = rawMeasureData.Flag;
                        curveData.Type1 = rawMeasureData.Type1;
                        curveData.Type = rawMeasureData.Type2;
                        curveData.Abs = double.Parse(rawMeasureData.Abs);
                        curveData.Abs1 = double.Parse(rawMeasureData.Abs1);
                        curveData.Abs2 = double.Parse(rawMeasureData.Abs2);
                        curveData.Abs3 = double.Parse(rawMeasureData.Abs3);
                        curveData.SignalMain1 = double.Parse(rawMeasureData.SignalMain1);
                        curveData.SignalRef1 = double.Parse(rawMeasureData.SignalRef1);
                        curveData.SignalMain2 = double.Parse(rawMeasureData.SignalMain2);
                        curveData.SignalRef2 = double.Parse(rawMeasureData.SignalRef2);
                        curveData.SignalMain3 = double.Parse(rawMeasureData.SignalMain3);
                        curveData.SignalRef3 = double.Parse(rawMeasureData.SignalRef3);

                        dataList.Add(curveData);
                    }
                    catch
                    {
                    }
                }

                return dataList;
            }
            catch(Exception ex)
            {
                throw new Exception($"从数据库提取测量数据出错：{ex.Message}");
            }
        }

        #endregion
    }
}