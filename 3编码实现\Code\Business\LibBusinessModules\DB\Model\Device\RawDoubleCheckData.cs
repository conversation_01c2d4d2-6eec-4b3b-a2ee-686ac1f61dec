﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 平行样测试数据
    ///</summary>
    [SugarTable("doublecheckData")]
    [Description("平行样测试数据")]
    public class RawDoubleCheckData
    {
        /// <summary>
        /// 时间
        ///</summary>
        [SugarColumn(ColumnName = "time")]
        [Description("时间")]
        public string? Time { get; set; }

        /// <summary>
        /// 第一次测量值
        ///</summary>
        [SugarColumn(ColumnName = "value1")]
        [Description("第一次测量值")]
        public string? Value1 { get; set; }

        /// <summary>
        /// 第二次测量值
        ///</summary>
        [SugarColumn(ColumnName = "value2")]
        [Description("第二次测量值")]
        public string? Value2 { get; set; }

        /// <summary>
        /// 第一次测量量时间
        ///</summary>
        [SugarColumn(ColumnName = "time1")]
        [Description("第一次测量量时间")]
        public string? Time1 { get; set; }

        /// <summary>
        /// 第二次测量量时间
        ///</summary>
        [SugarColumn(ColumnName = "time2")]
        [Description("第二次测量量时间")]
        public string? Time2 { get; set; }

        /// <summary>
        /// 误差
        ///</summary>
        [SugarColumn(ColumnName = "err")]
        [Description("误差")]
        public string? Err { get; set; }

        /// <summary>
        /// 误差判定
        ///</summary>
        [SugarColumn(ColumnName = "judge")]
        [Description("误差判定")]
        public string? Judge { get; set; }
    }
}