﻿using SqlSugar;
using System.ComponentModel;
using System.IO;
using System.Windows.Forms;

namespace LibBusinessModules.Config
{
    /// <summary>
    /// 数据库连接信息
    /// </summary>
    public class DBInfo
    {
        #region 字段属性

        /// <summary>
        /// 数据库类型
        /// </summary>
        [Description("数据库类型")]
        public DbType DatebaseType { get; set; } = DbType.Sqlite;

        /// <summary>
        /// 数据库名
        /// </summary>
        [Description("数据库名")]
        public string DatabaseName { get; set; }

        /// <summary>
        /// 数据库IP
        /// </summary>
        [Description("数据库IP")]
        public string ServerIP { get; set; }

        /// <summary>
        /// 数据库端口
        /// </summary>
        [Description("数据库端口")]
        public int ServerPort { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        [Description("用户名")]
        public string UserName { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        [Description("密码")]
        public string Password { get; set; }

        #endregion

        #region 公共方法

        public void GetConnectInfo(out string connectInfo, out DbType dbType)
        {
            dbType = DatebaseType;
            switch(dbType)
            {
                case DbType.MySql:
                    connectInfo = $"server={ServerIP};port={ServerPort};Database={DatabaseName};user={UserName};password={Password};charset=utf8;pooling=true";
                    break;
                case DbType.Sqlite:
                    connectInfo = @$"Data Source=DB\{DatabaseName}.db";
                    string path = Path.Combine(Application.StartupPath, @"DB");
                    if(!Directory.Exists(path))
                    {
                        Directory.CreateDirectory(path);
                    }
                    break;
                default:
                    throw new System.Exception($"当前不支持{dbType.ToString()}类型数据库！");
            }
        }

        #endregion
    }
}