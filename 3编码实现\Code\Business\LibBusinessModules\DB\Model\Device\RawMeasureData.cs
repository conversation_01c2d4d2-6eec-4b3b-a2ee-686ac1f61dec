﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 常规测量数据
    ///</summary>
    [SugarTable("measureData")]
    [Description("常规测量数据")]
    public class RawMeasureData
    {
        /// <summary>
        /// 时间
        ///</summary>
        [SugarColumn(ColumnName = "time")]
        [Description("时间")]
        public string? Time { get; set; }

        /// <summary>
        /// 浓度
        ///</summary>
        [SugarColumn(ColumnName = "value")]
        [Description("浓度")]
        public string? Value { get; set; }

        /// <summary>
        /// 量程
        ///</summary>
        [SugarColumn(ColumnName = "range")]
        [Description("量程")]
        public string? Range { get; set; }

        /// <summary>
        /// 数据标识
        ///</summary>
        [SugarColumn(ColumnName = "flag")]
        [Description("数据标识")]
        public string? Flag { get; set; }

        /// <summary>
        /// 类型1
        ///</summary>
        [SugarColumn(ColumnName = "type1")]
        [Description("类型1")]
        public string? Type1 { get; set; }

        /// <summary>
        /// 类型2
        ///</summary>
        [SugarColumn(ColumnName = "type")]
        [Description("类型2")]
        public string? Type2 { get; set; }

        /// <summary>
        /// 吸光度
        ///</summary>
        [SugarColumn(ColumnName = "abs")]
        [Description("吸光度")]
        public string? Abs { get; set; }

        /// <summary>
        /// 吸光度1
        ///</summary>
        [SugarColumn(ColumnName = "abs1")]
        [Description("吸光度1")]
        public string? Abs1 { get; set; }

        /// <summary>
        /// 吸光度2
        ///</summary>
        [SugarColumn(ColumnName = "abs2")]
        [Description("吸光度2")]
        public string? Abs2 { get; set; }

        /// <summary>
        /// 吸光度3
        ///</summary>
        [SugarColumn(ColumnName = "abs3")]
        [Description("吸光度3")]
        public string? Abs3 { get; set; }

        /// <summary>
        /// V1检测
        ///</summary>
        [SugarColumn(ColumnName = "signalMain1")]
        [Description("V1检测")]
        public string? SignalMain1 { get; set; }

        /// <summary>
        /// V1参比
        ///</summary>
        [SugarColumn(ColumnName = "signalRef1")]
        [Description("V1参比")]
        public string? SignalRef1 { get; set; }

        /// <summary>
        /// V2检测
        ///</summary>
        [SugarColumn(ColumnName = "signalMain2")]
        [Description("V2检测")]
        public string? SignalMain2 { get; set; }

        /// <summary>
        /// V2参比
        ///</summary>
        [SugarColumn(ColumnName = "signalRef2")]
        [Description("V2参比")]
        public string? SignalRef2 { get; set; }

        /// <summary>
        /// V3检测
        ///</summary>
        [SugarColumn(ColumnName = "signalMain3")]
        [Description("V3检测")]
        public string? SignalMain3 { get; set; }

        /// <summary>
        /// V3参比
        ///</summary>
        [SugarColumn(ColumnName = "signalRef3")]
        [Description("V3参比")]
        public string? SignalRef3 { get; set; }
    }
}