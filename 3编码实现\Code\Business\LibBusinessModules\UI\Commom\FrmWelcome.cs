﻿using LibBusinessModules.DB;
using LibBusinessModules.Helper;
using Sunny.UI;
using System;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace LibBusinessModules.UI.Commom
{
    partial class FrmWelcome : UIForm
    {
        public FrmWelcome()
        {
            InitializeComponent();
        }

        private void FrmWelcome_Load(object sender, EventArgs e)
        {
            // 读取标题
            lblTitle.Text = AppInfoHelper.AssemblyProduct;

            // 加载进度
            Task.Run(() =>
            {
                UpdatePgBarValue(10);

                // 加载配置文件
                GlobalHelper.InitializerConfig();

                UpdatePgBarValue(50);

                // 初始化数据库
                DBHelper.InitializeDB();

                UpdatePgBarValue(100);

                CloseForm();
            });
        }

        private void CloseForm()
        {
            if(this.InvokeRequired)
            {
                this.Invoke(new MethodInvoker(CloseForm));
            }
            else
            {
                this.Close();
            }
        }

        private void UpdatePgBarValue(int value)
        {
            if(this.InvokeRequired)
            {
                this.Invoke(new Action<int>(value2 => { pgBar.Value = value2; }), value);
            }
            else
            {
                pgBar.Value = value;
            }
        }
    }
}
