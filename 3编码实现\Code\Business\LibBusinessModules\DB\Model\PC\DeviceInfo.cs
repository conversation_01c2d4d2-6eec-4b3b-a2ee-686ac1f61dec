﻿using LibBaseModules.DB;
using LibBusinessModules.Config;
using LibBusinessModules.DB.Models.Device;
using Newtonsoft.Json;
using SqlSugar;
using System;
using System.ComponentModel;
using System.Linq;

namespace LibBusinessModules.DB.Models.PC
{
    /// <summary>
    /// 设备信息
    ///</summary>
    [SugarTable("device_info")]
    [Description("设备信息")]
    public class DeviceInfo : BaseNode
    {
        #region 字段属性

        #region 存储属性

        /// <summary>
        /// 序列号
        ///</summary>
        [SugarColumn(ColumnName = "sncode", ColumnDescription = "设备序列号", IsPrimaryKey = true)]
        [Description("序列号")]
        public string SNCode { get; set; }

        /// <summary>
        /// 主板软件版本
        ///</summary>
        [SugarColumn(ColumnName = "mainboardversion", ColumnDescription = "主板软件版本")]
        [Description("主板软件版本")]
        public string MainBoardVersion { get; set; }

        /// <summary>
        /// 屏幕软件版本
        ///</summary>
        [SugarColumn(ColumnName = "hmiversion", ColumnDescription = "屏幕软件版本")]
        [Description("屏幕软件版本")]
        public string HmiVersion { get; set; }

        /// <summary>
        /// 流路版本
        ///</summary>
        [SugarColumn(ColumnName = "flowversion", ColumnDescription = "流路版本")]
        [Description("流路版本")]
        public string FlowVersion { get; set; }

        /// <summary>
        /// 柱塞泵软件版本
        ///</summary>
        [SugarColumn(ColumnName = "pumpversion", ColumnDescription = "柱塞泵软件版本")]
        [Description("柱塞泵软件版本")]
        public string PumpVersion { get; set; }

        /// <summary>
        /// 柱塞泵序列号
        ///</summary>
        [SugarColumn(ColumnName = "pumpsn", ColumnDescription = "柱塞泵序列号")]
        [Description("柱塞泵序列号")]
        public string PumpSN { get; set; }

        /// <summary>
        /// 选向阀软件版本
        ///</summary>
        [SugarColumn(ColumnName = "valveversion", ColumnDescription = "选向阀软件版本")]
        [Description("选向阀软件版本")]
        public string ValveVersion { get; set; }

        /// <summary>
        /// 选向阀序列号
        ///</summary>
        [SugarColumn(ColumnName = "valvesn", ColumnDescription = "选向阀序列号")]
        [Description("选向阀序列号")]
        public string ValveSN { get; set; }

        #endregion

        #region 辅助属性

        /// <summary>
        /// 当前设备是否为COD设备
        /// 只读属性，不序列化
        ///</summary>
        [Description("当前设备是否为COD设备")]
        [JsonIgnore]
        public bool IsCODDevice => !string.IsNullOrEmpty(SNCode) && SNCode.ToLower().StartsWith("fa0101");

        /// <summary>
        /// 当前设备是否为NH4设备
        /// 只读属性，不序列化
        ///</summary>
        [Description("当前设备是否为NH4设备")]
        [JsonIgnore]
        public bool IsNH4Device => !string.IsNullOrEmpty(SNCode) && SNCode.ToLower().StartsWith("fa0102");

        /// <summary>
        /// 当前设备是否为TP设备
        /// 只读属性，不序列化
        ///</summary>
        [Description("当前设备是否为TP设备")]
        [JsonIgnore]
        public bool IsTPDevice => !string.IsNullOrEmpty(SNCode) && SNCode.ToLower().StartsWith("fa0103");

        /// <summary>
        /// 当前设备是否为TN设备
        /// 只读属性，不序列化
        ///</summary>
        [Description("当前设备是否为TN设备")]
        [JsonIgnore]
        public bool IsTNDevice => !string.IsNullOrEmpty(SNCode) && SNCode.ToLower().StartsWith("fa0104");

        /// <summary>
        /// 当前设备是否为IMN设备
        /// 只读属性，不序列化
        ///</summary>
        [Description("当前设备是否为IMN设备")]
        [JsonIgnore]
        public bool IsIMNDevice => !string.IsNullOrEmpty(SNCode) && SNCode.ToLower().StartsWith("fa0105");

        #endregion

        #endregion

        #region 公共方法

        /// <summary>
        /// 从设备数据库生成数据
        /// </summary>
        /// <param name="db"></param>
        /// <exception cref="Exception"></exception>
        public void InitFromDeviceDB(SqlSugarClient db)
        {
            try
            {
                var deviceSn = db.Queryable<DeviceSNInfo>().First();
                if(deviceSn == null || string.IsNullOrEmpty(deviceSn.SN))
                {
                    throw new Exception("设备序列号为空!");
                }
                var mainVersion = db.Queryable<MainVersionInfo>().First();
                if(mainVersion == null || string.IsNullOrEmpty(mainVersion.Version))
                {
                    throw new Exception("主板软件版本为空!");
                }
                var hmiVersion = db.Queryable<HmiVersionInfo>().First();
                if(hmiVersion == null || string.IsNullOrEmpty(hmiVersion.Version))
                {
                    throw new Exception("屏幕软件版本为空!");
                }
                var flowVersion = db.Queryable<FlowVersionInfo>().First();
                if(flowVersion == null || string.IsNullOrEmpty(flowVersion.Version))
                {
                    throw new Exception("流路版本为空!");
                }
                var pumpVersion = db.Queryable<PumpVersionInfo>().First();
                if(pumpVersion == null || string.IsNullOrEmpty(pumpVersion.Version))
                {
                    throw new Exception("柱塞泵软件版本为空!");
                }
                var pumpSn = db.Queryable<PumpSNInfo>().First();
                if(pumpSn == null || string.IsNullOrEmpty(pumpSn.SN))
                {
                    throw new Exception("柱塞泵序列号为空!");
                }
                var valveVersion = db.Queryable<ValveVersionInfo>().First();
                if(valveVersion == null || string.IsNullOrEmpty(valveVersion.Version))
                {
                    throw new Exception("选向阀软件版本为空!");
                }
                var valveSn = db.Queryable<ValveSNInfo>().First();
                if(valveSn == null || string.IsNullOrEmpty(valveSn.SN))
                {
                    throw new Exception("选向阀序列号为空!");
                }

                // 基础信息提取
                SNCode = deviceSn.SN;
                MainBoardVersion = mainVersion.Version;
                HmiVersion = hmiVersion.Version;
                FlowVersion = flowVersion.Version;
                PumpVersion = pumpVersion.Version;
                PumpSN = pumpSn.SN;
                ValveVersion = valveVersion.Version;
                ValveSN = valveSn.SN;
            }
            catch(Exception ex)
            {
                throw new Exception($"从数据库提取基础信息出错：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取设备类型
        /// </summary>
        /// <returns></returns>
        public string GetDeviceType()
        {
            if(!string.IsNullOrEmpty(SNCode) && SNCode.Length > 6)
            {
                var device = DeviceManager.GetInstance().GetDeviceList().FirstOrDefault(x => x.IdCode == SNCode.Substring(0, 6));
                if(device != null)
                {
                    return device.Model;
                }
                else
                {
                    return "其他常规";
                }
            }
            return "非法序列号";
        }

        #endregion
    }
}