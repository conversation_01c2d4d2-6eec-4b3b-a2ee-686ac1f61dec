﻿using LibBusinessModules.Report.Config;
using System;
using System.IO;

namespace LibBusinessModules.Report.Helper
{
    /// <summary>
    /// 报告路径帮助类
    /// </summary>
    internal class ReportPathHelper
    {
        /// <summary>
        /// 报表文件文件夹
        /// </summary>
        private const string ReportFileFolder = "Reports";

        /// <summary>
        /// 模板文件文件夹
        /// </summary>
        private const string TemplatesFileFolder = "Templates";

        /// <summary>
        /// 获取报告保存路径
        /// </summary>
        /// <returns>报告保存路径</returns>
        public static string GetReportSavePath(ReportCalculateNode calculateNode, string reportType)
        {
            // 因子型号
            var type = calculateNode.DeviceConfigInfo.Factor;
            // 设备序列号
            var sn = calculateNode.SNCode;
            // 拼接文件名
            var fileName = $"{reportType}-{sn}.docx";
            // 拼接完整路径
            var path = Path.Combine(Environment.CurrentDirectory, ReportFileFolder, type, sn);

            // 创建文件夹
            if(!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }

            return Path.Combine(path, fileName);
        }

        /// <summary>
        /// 获取模板文件路径
        /// </summary>
        /// <param name="calculateNode"></param>
        /// <param name="reportType"></param>
        /// <returns></returns>
        public static string GetTemplateFilePath(ReportCalculateNode calculateNode, string reportType)
        {
            // ID识别码
            var idCode = calculateNode.DeviceConfigInfo.IdCode;

            // 拼接完整路径
            var path = Path.Combine(Environment.CurrentDirectory, TemplatesFileFolder, reportType + "模板", idCode + ".docx");

            if(!File.Exists(path))
            {
                path = Path.Combine(Environment.CurrentDirectory, TemplatesFileFolder, reportType + "模板", "Default.docx");
            }

            if(!File.Exists(path))
            {
                throw new Exception($"因子类型{calculateNode.DeviceConfigInfo.Factor},ID识别码{idCode}，找不到对应{reportType}模板文件[{path}]，请重新标记模板文件后重试！");
            }
            else
            {
                return path;
            }
        }
    }
}
