using System;
using System.IO;
using System.Net;

namespace Fpi.Util
{
    public static class FTPHelper
    {
        /// <summary>
        /// 测试FTP连接是否有效
        /// </summary>
        /// <param name="ftpServerIp"></param>
        /// <param name="ftpPort"></param>
        /// <returns></returns>
        static bool TestFtpConnection(string ftpServerIp, string ftpPort, string userName, string password)
        {
            try
            {
                FtpWebRequest request = (FtpWebRequest)WebRequest.Create($"ftp://{ftpServerIp}:{ftpPort}/");
                request.Method = WebRequestMethods.Ftp.ListDirectory;
                request.Credentials = new NetworkCredential(userName, password);
                request.Timeout = 5000; // 设置超时时间为5秒

                using(FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                {
                    return true;
                }
            }
            catch(WebException ex)
            {
                return false;
            }
            catch(Exception ex)
            {
                return false;
            }
        }

        /// <summary>
        /// 下载FTP文件到本地
        /// </summary>
        /// <param name="ftpServerIp"></param>
        /// <param name="ftpPort"></param>
        /// <param name="remoteFilePath"></param>
        /// <param name="localFilePath"></param>
        public static void DownloadFileFromFtp(string ftpServerIp, string ftpPort, string userName, string password, string remoteFilePath, string localFilePath)
        {
            try
            {
                string directoryPath = Path.GetDirectoryName(localFilePath);
                if(!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }

                FtpWebRequest request = (FtpWebRequest)WebRequest.Create($"ftp://{ftpServerIp}:{ftpPort}/{remoteFilePath}");
                request.Method = WebRequestMethods.Ftp.DownloadFile;
                request.Credentials = new NetworkCredential(userName, password);

                using(FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                using(Stream responseStream = response.GetResponseStream())
                using(FileStream fileStream = File.Create(localFilePath))
                {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while((bytesRead = responseStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        fileStream.Write(buffer, 0, bytesRead);
                    }
                }
            }
            catch(Exception e)
            {
                throw new Exception($"从FTP服务器下载文件出错：{e.Message}");
            }
        }

        /// <summary>
        /// 读取FTP文件到内存中
        /// </summary>
        /// <param name="ftpServerIp"></param>
        /// <param name="ftpPort"></param>
        /// <param name="remoteFilePath"></param>
        /// <returns></returns>
        public static byte[] DownloadFileToMemory(string ftpServerIp, string ftpPort, string userName, string password, string remoteFilePath)
        {
            try
            {
                FtpWebRequest request = (FtpWebRequest)WebRequest.Create($"ftp://{ftpServerIp}:{ftpPort}/{remoteFilePath}");
                request.Method = WebRequestMethods.Ftp.DownloadFile;
                request.Credentials = new NetworkCredential(userName, password);

                using(FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                using(Stream responseStream = response.GetResponseStream())
                using(MemoryStream memoryStream = new MemoryStream())
                {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while((bytesRead = responseStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        memoryStream.Write(buffer, 0, bytesRead);
                    }
                    return memoryStream.ToArray();
                }
            }
            catch(Exception e)
            {
                throw new Exception($"从FTP服务器读取文件出错：{e.Message}");
            }
        }
    }
}