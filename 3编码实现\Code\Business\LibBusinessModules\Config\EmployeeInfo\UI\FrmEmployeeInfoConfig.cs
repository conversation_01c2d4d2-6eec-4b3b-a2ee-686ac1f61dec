﻿using Sunny.UI;
using System;
using System.Linq;
using System.Windows.Forms;

namespace LibBusinessModules.Config.UI
{
    /// <summary>
    /// 员工信息编辑
    /// </summary>
    public partial class FrmEmployeeInfoConfig : UIForm
    {
        #region 字段属性

        /// <summary>
        /// 界面对应员工信息
        /// </summary>
        public EmployeeInfo TargetEmployeeInfo;

        /// <summary>
        /// 当前处于编辑模式
        /// </summary>
        private bool _isEdit;

        #endregion

        #region 构造

        public FrmEmployeeInfoConfig()
        {
            InitializeComponent();
        }

        public FrmEmployeeInfoConfig(EmployeeInfo employeeInfo, bool isEdit = false) : this()
        {
            TargetEmployeeInfo = employeeInfo;
            _isEdit = isEdit;
        }

        #endregion

        #region 事件

        private void FrmDeviceConfig_Load(object sender, EventArgs e)
        {
            // 编辑模式下，工号不可修改
            txtID.Enabled = !_isEdit;
            if(TargetEmployeeInfo != null)
            {
                txtID.Text = TargetEmployeeInfo.ID;
                txtName.Text = TargetEmployeeInfo.Name;
                txtPosition.Text = TargetEmployeeInfo.Position;

            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                Check();

                TargetEmployeeInfo.ID = txtID.Text;
                TargetEmployeeInfo.Name = txtName.Text;
                TargetEmployeeInfo.Position = txtPosition.Text;

                DialogResult = DialogResult.OK;
                this.Close();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"保存失败：{ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        private void Check()
        {
            if(string.IsNullOrWhiteSpace(txtID.Text))
            {
                throw new Exception("工号不能为空！");
            }
            if(string.IsNullOrWhiteSpace(txtName.Text))
            {
                throw new Exception("姓名不能为空！");
            }
            if(string.IsNullOrWhiteSpace(txtPosition.Text))
            {
                throw new Exception("岗位不能为空！");
            }

            // 新增模式下，校验工号是否重复
            if(!_isEdit)
            {
                if(SystemConfig.GetInstance().GetAllEmployeeInfo().Any(x => x.ID == txtID.Text))
                {
                    throw new Exception("工号重复！");
                }
            }

        }

        #endregion
    }
}