﻿using LibBaseModules.DB;
using LibBusinessModules.DB.Models.Device;
using SqlSugar;
using System;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.PC
{
    /// <summary>
    /// 光源信息
    ///</summary>
    [SugarTable("light_source_info")]
    [Description("光源信息")]
    public class LightSourceInfo : BaseNode
    {
        #region 字段属性

        /// <summary>
        /// 设备序列号
        ///</summary>
        [SugarColumn(ColumnName = "sncode", IsPrimaryKey = true)]
        [Description("设备序列号")]
        public string SNCode { get; set; }

        /// <summary>
        /// 数据时间
        ///</summary>
        [SugarColumn(ColumnName = "time", SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "数据时间")]
        [Description("数据时间")]
        public DateTime Time { get; set; }

        /// <summary>
        /// 信号电流
        ///</summary>
        [SugarColumn(ColumnName = "current", ColumnDescription = "信号电流")]
        [Description("信号电流")]
        public double Current { get; set; }

        /// <summary>
        /// 检测信号档位
        ///</summary>
        [SugarColumn(ColumnName = "main_index", ColumnDescription = "检测信号档位")]
        [Description("检测信号档位")]
        public int MainIndex { get; set; }

        /// <summary>
        /// 检测电压1
        ///</summary>
        [SugarColumn(ColumnName = "main_v1", ColumnDescription = "检测电压1")]
        [Description("检测电压1")]
        public double MainV1 { get; set; }

        /// <summary>
        /// 检测电压2
        ///</summary>
        [SugarColumn(ColumnName = "main_v2", ColumnDescription = "检测电压2")]
        [Description("检测电压2")]
        public double MainV2 { get; set; }

        /// <summary>
        /// 检测电压3
        ///</summary>
        [SugarColumn(ColumnName = "main_v3", ColumnDescription = "检测电压3")]
        [Description("检测电压3")]
        public double MainV3 { get; set; }

        /// <summary>
        /// 检测电压4
        ///</summary>
        [SugarColumn(ColumnName = "main_v4", ColumnDescription = "检测电压4")]
        [Description("检测电压4")]
        public double MainV4 { get; set; }

        /// <summary>
        /// 参比信号档位
        ///</summary>
        [SugarColumn(ColumnName = "ref_index", ColumnDescription = "参比信号档位")]
        [Description("参比信号档位")]
        public int RefIndex { get; set; }

        /// <summary>
        /// 参比电压1
        ///</summary>
        [SugarColumn(ColumnName = "ref_v1", ColumnDescription = "参比电压1")]
        [Description("参比电压1")]
        public double RefV1 { get; set; }

        /// <summary>
        /// 参比电压2
        ///</summary>
        [SugarColumn(ColumnName = "ref_v2", ColumnDescription = "参比电压2")]
        [Description("参比电压2")]
        public double RefV2 { get; set; }

        /// <summary>
        /// 参比电压3
        ///</summary>
        [SugarColumn(ColumnName = "ref_v3", ColumnDescription = "参比电压3")]
        [Description("参比电压3")]
        public double RefV3 { get; set; }

        /// <summary>
        /// 参比电压4
        ///</summary>
        [SugarColumn(ColumnName = "ref_v4", ColumnDescription = "参比电压4")]
        [Description("参比电压4")]
        public double RefV4 { get; set; }

        #endregion

        #region 公共方法

        /// <summary>
        /// 从设备数据库生成数据
        /// </summary>
        public void InitFromDeviceDB(SqlSugarClient db, string snCode)
        {
            try
            {
                var adjSignalInfo = db.Queryable<AdjSignalInfo>().OrderBy(it => it.Time, OrderByType.Desc).First();
                if(adjSignalInfo == null)
                {
                    throw new Exception($"光源信息为空!");
                }

                SNCode = snCode;
                Time = DateTime.Parse(adjSignalInfo.Time);
                Current = double.Parse(adjSignalInfo.Current);
                MainIndex = int.Parse(adjSignalInfo.MainIndex);
                MainV1 = double.Parse(adjSignalInfo.MainV1);
                MainV2 = double.Parse(adjSignalInfo.MainV2);
                MainV3 = double.Parse(adjSignalInfo.MainV3);
                MainV4 = double.Parse(adjSignalInfo.MainV4);
                RefIndex = int.Parse(adjSignalInfo.RefIndex);
                RefV1 = double.Parse(adjSignalInfo.RefV1);
                RefV2 = double.Parse(adjSignalInfo.RefV2);
                RefV3 = double.Parse(adjSignalInfo.RefV3);
                RefV4 = double.Parse(adjSignalInfo.RefV4);
            }
            catch(Exception ex)
            {
                throw new Exception($"从数据库提取光源信息出错：{ex.Message}");
            }
        }

        #endregion
    }
}