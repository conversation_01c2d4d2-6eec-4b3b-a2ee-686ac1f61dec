using System;
using System.Collections.Generic;

namespace LibBusinessModules.Config.FaultManagement.Models
{
    /// <summary>
    /// 完整的故障数据结构
    /// 完整的故障数据结构，主要包含一级故障分类列表。
    /// 此类作为故障配置的根节点。
    /// </summary>
    public class FaultData
    {
        /// <summary>
        /// 获取或设置一级故障分类列表。
        /// </summary>
        public List<FaultCategory1> Category1List { get; set; } = new List<FaultCategory1>();

    }

    /// <summary>
    /// 故障一级分类模型
    /// 表示故障的一级分类。
    /// </summary>
    public class FaultCategory1
    {
        /// <summary>
        /// 获取或设置一级分类的唯一标识ID。
        /// </summary>
        public int Id { get; set; }
        /// <summary>
        /// 获取或设置一级分类的名称。
        /// </summary>
        public string CategoryName { get; set; }
        /// <summary>
        /// 获取或设置一级分类的描述信息。
        /// </summary>
        public string Description { get; set; }

        // 包含的二级分类
        /// <summary>
        /// 获取或设置此一级分类下包含的二级分类列表。
        /// </summary>
        public List<FaultCategory2> SubCategories { get; set; } = new List<FaultCategory2>();
    }

    /// <summary>
    /// 故障二级分类模型
    /// 表示故障的二级分类。
    /// </summary>
    public class FaultCategory2
    {
        /// <summary>
        /// 获取或设置二级分类的唯一标识ID。
        /// </summary>
        public int Id { get; set; }
        /// <summary>
        /// 获取或设置此二级分类所属的一级分类的ID。
        /// </summary>
        public int Category1Id { get; set; }
        /// <summary>
        /// 获取或设置二级分类的名称。
        /// </summary>
        public string CategoryName { get; set; }
        /// <summary>
        /// 获取或设置二级分类的描述信息。
        /// </summary>
        public string Description { get; set; }
    }

    /// <summary>
    /// 故障记录模型
    /// 表示一个具体的故障记录。
    /// </summary>
    public class FaultRecord
    {
        /// <summary>
        /// 获取或设置故障记录的唯一标识ID。
        /// </summary>
        public int Id { get; set; }
        /// <summary>
        /// 获取或设置故障所属的一级分类ID。
        /// </summary>
        public int Category1Id { get; set; }
        /// <summary>
        /// 获取或设置故障所属的二级分类ID。
        /// </summary>
        public int Category2Id { get; set; }
        /// <summary>
        /// 获取或设置故障的标题。
        /// </summary>
        public string FaultTitle { get; set; }
        /// <summary>
        /// 获取或设置故障的详细描述。
        /// </summary>
        public string FaultDescription { get; set; }
        /// <summary>
        /// 获取或设置故障的严重程度，默认为 Low。
        /// </summary>
        public FaultSeverity Severity { get; set; } = FaultSeverity.Low;
        /// <summary>
        /// 获取或设置故障的当前状态，默认为 Pending。
        /// </summary>
        public FaultStatus Status { get; set; } = FaultStatus.Pending;
        /// <summary>
        /// 获取或设置报告者的ID。
        /// </summary>
        public string ReporterId { get; set; }
        /// <summary>
        /// 获取或设置报告者的名称。
        /// </summary>
        public string ReporterName { get; set; }
        /// <summary>
        /// 获取或设置故障的创建时间，默认为当前时间。
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;
        /// <summary>
        /// 获取或设置故障的最后更新时间，默认为当前时间。
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;
        /// <summary>
        /// 获取或设置故障的解决时间，如果尚未解决则为 null。
        /// </summary>
        public DateTime? ResolveTime { get; set; }
    }

    /// </summary>
    public enum FaultSeverity
    {
        /// <summary>
        /// 低严重程度。
        /// </summary>
        Low = 1,
        /// <summary>
        /// 中等严重程度。
        /// </summary>
        Medium = 2,
        /// <summary>
        /// 高严重程度。
        /// </summary>
        High = 3,
        /// <summary>
        /// 紧急严重程度。
        /// </summary>
        Critical = 4
    }

    /// <summary>
    /// 故障状态枚举
    /// 表示故障处理的当前状态。
    /// </summary>
    public enum FaultStatus
    {
        /// <summary>
        /// 故障待处理
        /// </summary>
        Pending = 1,
        /// <summary>
        /// 故障处理中
        /// </summary>
        InProgress = 2,
        /// <summary>
        /// 故障已解决
        /// </summary>
        Resolved = 3,
        /// <summary>
        /// 故障已关闭
        /// </summary>
        Closed = 4
    }
}