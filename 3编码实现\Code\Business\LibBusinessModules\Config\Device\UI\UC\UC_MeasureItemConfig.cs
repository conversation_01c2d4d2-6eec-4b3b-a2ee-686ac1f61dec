﻿using Sunny.UI;
using System;

namespace LibBusinessModules.Config.UI
{
    /// <summary>
    /// 单个测试项编辑
    /// </summary>
    public partial class UC_MeasureItemConfig : UIUserControl
    {
        #region 字段属性

        private MeasureItem _measureItem;

        #endregion

        #region 构造

        public UC_MeasureItemConfig()
        {
            InitializeComponent();
        }

        public UC_MeasureItemConfig(MeasureItem measureItem) : this()
        {
            _measureItem = measureItem;
        }

        #endregion

        #region 事件

        private void UC_MeasureItemConfig_Load(object sender, EventArgs e)
        {
            if(_measureItem != null)
            {
                chkIsUsed.Text = _measureItem.Name;
                chkIsUsed.Checked = _measureItem.IsUsed;
                txtValue.Text = _measureItem.StandValue.ToString("F4");
                txtQualifiedStandard.Text = _measureItem.QualifiedStandard.ToString("F4");
                rdbIsAbsolute.Checked = _measureItem.IsAbsolute;
            }
        }

        #endregion

        #region 公共方法

        public void Check()
        {
            // 勾选时校验
            if(chkIsUsed.Checked)
            {
                if(!double.TryParse(txtValue.Text, out double standardRange))
                {
                    throw new Exception($"{_measureItem.Name}浓度值必须为数字！");
                }
                if(!double.TryParse(txtQualifiedStandard.Text, out double qualifiedStandard))
                {
                    throw new Exception($"{_measureItem.Name}合格标准必须为数字！");
                }
            }
        }

        public void Save()
        {
            _measureItem.IsUsed = chkIsUsed.Checked;
            if(chkIsUsed.Checked)
            {
                _measureItem.StandValue = double.Parse(txtValue.Text);
                _measureItem.QualifiedStandard = double.Parse(txtQualifiedStandard.Text);
            }
        }

        #endregion
    }
}