﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 高指校准数据
    ///</summary>
    [SugarTable("IMN_adjData")]
    [Description("高指校准数据")]
    public class ImnRawAdjdata
    {
        /// <summary>
        /// 时间
        ///</summary>
        [SugarColumn(ColumnName = "time")]
        [Description("时间")]
        public string? Time { get; set; }

        /// <summary>
        /// 浓度
        ///</summary>
        [SugarColumn(ColumnName = "value")]
        [Description("浓度")]
        public string? Value { get; set; }

        /// <summary>
        /// 量程
        ///</summary>
        [SugarColumn(ColumnName = "range")]
        [Description("量程")]
        public string? Range { get; set; }

        /// <summary>
        /// 数据标识
        ///</summary>
        [SugarColumn(ColumnName = "flag")]
        [Description("数据标识")]
        public string? Flag { get; set; }

        /// <summary>
        /// 类型1
        ///</summary>
        [SugarColumn(ColumnName = "type1")]
        [Description("类型1")]
        public string? Type1 { get; set; }

        /// <summary>
        /// 类型2
        ///</summary>
        [SugarColumn(ColumnName = "type")]
        [Description("类型2")]
        public string? Type { get; set; }

        /// <summary>
        /// 修正滴定数
        ///</summary>
        [SugarColumn(ColumnName = "titration_amend")]
        [Description("修正滴定数")]
        public string? TitrationAmend { get; set; }

        /// <summary>
        /// 滴定数
        ///</summary>
        [SugarColumn(ColumnName = "titration")]
        [Description("滴定数")]
        public string? Titration { get; set; }

        /// <summary>
        /// 吸光度1
        ///</summary>
        [SugarColumn(ColumnName = "abs1")]
        [Description("吸光度1")]
        public string? Abs1 { get; set; }

        /// <summary>
        /// 检测信号1
        ///</summary>
        [SugarColumn(ColumnName = "signalMain1")]
        [Description("检测信号1")]
        public string? SignalMain1 { get; set; }

        /// <summary>
        /// 参比信号1
        ///</summary>
        [SugarColumn(ColumnName = "signalRef1")]
        [Description("参比信号1")]
        public string? SignalRef1 { get; set; }

        /// <summary>
        /// 吸光度2
        ///</summary>
        [SugarColumn(ColumnName = "abs2")]
        [Description("吸光度2")]
        public string? Abs2 { get; set; }

        /// <summary>
        /// 检测信号2
        ///</summary>
        [SugarColumn(ColumnName = "signalMain2")]
        [Description("检测信号2")]
        public string? SignalMain2 { get; set; }

        /// <summary>
        /// 参比信号2
        ///</summary>
        [SugarColumn(ColumnName = "signalRef2")]
        [Description("参比信号2")]
        public string? SignalRef2 { get; set; }
    }
}