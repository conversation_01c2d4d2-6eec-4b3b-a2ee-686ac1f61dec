﻿namespace LibBusinessModules.Report.UI
{
    partial class UC_ReportDataShow
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle16 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle17 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle18 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle19 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle20 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle21 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle22 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle23 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle24 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle25 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle26 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle27 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle28 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle29 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle30 = new System.Windows.Forms.DataGridViewCellStyle();
            this.gbDataDetail = new Sunny.UI.UIGroupBox();
            this.tabMain = new Sunny.UI.UITabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.dgvMeasureData = new Sunny.UI.UIDataGridView();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.dgvCalibrationData = new Sunny.UI.UIDataGridView();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.dgvCurveData = new Sunny.UI.UIDataGridView();
            this.pnlTop = new Sunny.UI.UITitlePanel();
            this.uiGroupBox5 = new Sunny.UI.UIGroupBox();
            this.uiLabel31 = new Sunny.UI.UILabel();
            this.uiLabel32 = new Sunny.UI.UILabel();
            this.cmbAuditor = new Sunny.UI.UIComboBox();
            this.uiLabel30 = new Sunny.UI.UILabel();
            this.txtTemp = new Sunny.UI.UITextBox();
            this.uiLabel27 = new Sunny.UI.UILabel();
            this.cmbInspector = new Sunny.UI.UIComboBox();
            this.uiLabel29 = new Sunny.UI.UILabel();
            this.uiLabel28 = new Sunny.UI.UILabel();
            this.txtHumidity = new Sunny.UI.UITextBox();
            this.uiGroupBox3 = new Sunny.UI.UIGroupBox();
            this.uiLabel20 = new Sunny.UI.UILabel();
            this.txtRefV4 = new Sunny.UI.UITextBox();
            this.uiLabel21 = new Sunny.UI.UILabel();
            this.txtRefV3 = new Sunny.UI.UITextBox();
            this.uiLabel22 = new Sunny.UI.UILabel();
            this.txtRefV2 = new Sunny.UI.UITextBox();
            this.uiLabel23 = new Sunny.UI.UILabel();
            this.txtRefV1 = new Sunny.UI.UITextBox();
            this.uiLabel24 = new Sunny.UI.UILabel();
            this.txtRefIndex = new Sunny.UI.UITextBox();
            this.uiLabel25 = new Sunny.UI.UILabel();
            this.uiLabel15 = new Sunny.UI.UILabel();
            this.txtMainV4 = new Sunny.UI.UITextBox();
            this.uiLabel13 = new Sunny.UI.UILabel();
            this.txtMainV3 = new Sunny.UI.UITextBox();
            this.uiLabel14 = new Sunny.UI.UILabel();
            this.txtMainV2 = new Sunny.UI.UITextBox();
            this.uiLabel7 = new Sunny.UI.UILabel();
            this.txtMainV1 = new Sunny.UI.UITextBox();
            this.uiLabel8 = new Sunny.UI.UILabel();
            this.txtMainIndex = new Sunny.UI.UITextBox();
            this.uiLabel9 = new Sunny.UI.UILabel();
            this.uiGroupBox2 = new Sunny.UI.UIGroupBox();
            this.txtValveSN = new Sunny.UI.UITextBox();
            this.uiLabel19 = new Sunny.UI.UILabel();
            this.txtValveVersion = new Sunny.UI.UITextBox();
            this.uiLabel16 = new Sunny.UI.UILabel();
            this.txtPumpSN = new Sunny.UI.UITextBox();
            this.uiLabel17 = new Sunny.UI.UILabel();
            this.txtPumpVersion = new Sunny.UI.UITextBox();
            this.uiLabel18 = new Sunny.UI.UILabel();
            this.txtFlowVersion = new Sunny.UI.UITextBox();
            this.uiLabel10 = new Sunny.UI.UILabel();
            this.txtHmiVersion = new Sunny.UI.UITextBox();
            this.uiLabel11 = new Sunny.UI.UILabel();
            this.txtMainBoardVersion = new Sunny.UI.UITextBox();
            this.uiLabel12 = new Sunny.UI.UILabel();
            this.uiGroupBox1 = new Sunny.UI.UIGroupBox();
            this.txtSNCode = new Sunny.UI.UITextBox();
            this.uiLabel26 = new Sunny.UI.UILabel();
            this.lblAuxiliaryRangeUnit = new Sunny.UI.UILabel();
            this.lblStandardRangeUnit = new Sunny.UI.UILabel();
            this.txtAuxiliaryRange = new Sunny.UI.UITextBox();
            this.uiLabel6 = new Sunny.UI.UILabel();
            this.txtStandardRange = new Sunny.UI.UITextBox();
            this.uiLabel5 = new Sunny.UI.UILabel();
            this.txtInspectionBasis = new Sunny.UI.UITextBox();
            this.uiLabel4 = new Sunny.UI.UILabel();
            this.txtIdCode = new Sunny.UI.UITextBox();
            this.uiLabel3 = new Sunny.UI.UILabel();
            this.txtModel = new Sunny.UI.UITextBox();
            this.uiLabel2 = new Sunny.UI.UILabel();
            this.txtFactor = new Sunny.UI.UITextBox();
            this.uiLabel1 = new Sunny.UI.UILabel();
            this.gbDataDetail.SuspendLayout();
            this.tabMain.SuspendLayout();
            this.tabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvMeasureData)).BeginInit();
            this.tabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvCalibrationData)).BeginInit();
            this.tabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvCurveData)).BeginInit();
            this.pnlTop.SuspendLayout();
            this.uiGroupBox5.SuspendLayout();
            this.uiGroupBox3.SuspendLayout();
            this.uiGroupBox2.SuspendLayout();
            this.uiGroupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // gbDataDetail
            // 
            this.gbDataDetail.Controls.Add(this.tabMain);
            this.gbDataDetail.Controls.Add(this.pnlTop);
            this.gbDataDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbDataDetail.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbDataDetail.Location = new System.Drawing.Point(1, 1);
            this.gbDataDetail.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbDataDetail.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbDataDetail.Name = "gbDataDetail";
            this.gbDataDetail.Padding = new System.Windows.Forms.Padding(2, 32, 2, 2);
            this.gbDataDetail.Size = new System.Drawing.Size(1198, 815);
            this.gbDataDetail.TabIndex = 0;
            this.gbDataDetail.Text = "数据详情";
            this.gbDataDetail.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // tabMain
            // 
            this.tabMain.Controls.Add(this.tabPage1);
            this.tabMain.Controls.Add(this.tabPage2);
            this.tabMain.Controls.Add(this.tabPage3);
            this.tabMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabMain.DrawMode = System.Windows.Forms.TabDrawMode.OwnerDrawFixed;
            this.tabMain.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tabMain.ItemSize = new System.Drawing.Size(150, 40);
            this.tabMain.Location = new System.Drawing.Point(2, 364);
            this.tabMain.MainPage = "";
            this.tabMain.MenuStyle = Sunny.UI.UIMenuStyle.Custom;
            this.tabMain.Name = "tabMain";
            this.tabMain.SelectedIndex = 0;
            this.tabMain.Size = new System.Drawing.Size(1194, 449);
            this.tabMain.SizeMode = System.Windows.Forms.TabSizeMode.Fixed;
            this.tabMain.TabBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.tabMain.TabIndex = 1;
            this.tabMain.TabPageTextAlignment = System.Windows.Forms.HorizontalAlignment.Center;
            this.tabMain.TabSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
            this.tabMain.TabUnSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.tabMain.TabUnSelectedForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.tabMain.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.dgvMeasureData);
            this.tabPage1.Location = new System.Drawing.Point(0, 40);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Size = new System.Drawing.Size(1194, 409);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "测量数据";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // dgvMeasureData
            // 
            this.dgvMeasureData.AllowUserToAddRows = false;
            this.dgvMeasureData.AllowUserToDeleteRows = false;
            this.dgvMeasureData.AllowUserToResizeRows = false;
            dataGridViewCellStyle16.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle16.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.dgvMeasureData.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle16;
            this.dgvMeasureData.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvMeasureData.AutoSizeRowsMode = System.Windows.Forms.DataGridViewAutoSizeRowsMode.AllCells;
            this.dgvMeasureData.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.dgvMeasureData.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle17.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle17.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle17.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle17.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle17.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle17.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle17.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvMeasureData.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle17;
            this.dgvMeasureData.ColumnHeadersHeight = 32;
            this.dgvMeasureData.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            dataGridViewCellStyle18.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle18.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle18.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle18.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle18.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(236)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle18.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle18.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvMeasureData.DefaultCellStyle = dataGridViewCellStyle18;
            this.dgvMeasureData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvMeasureData.EnableHeadersVisualStyles = false;
            this.dgvMeasureData.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.dgvMeasureData.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(173)))), ((int)(((byte)(255)))));
            this.dgvMeasureData.Location = new System.Drawing.Point(0, 0);
            this.dgvMeasureData.Name = "dgvMeasureData";
            this.dgvMeasureData.RightToLeft = System.Windows.Forms.RightToLeft.No;
            dataGridViewCellStyle19.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle19.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle19.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle19.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle19.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle19.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle19.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvMeasureData.RowHeadersDefaultCellStyle = dataGridViewCellStyle19;
            this.dgvMeasureData.RowHeadersVisible = false;
            dataGridViewCellStyle20.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle20.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle20.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle20.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(236)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle20.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.dgvMeasureData.RowsDefaultCellStyle = dataGridViewCellStyle20;
            this.dgvMeasureData.RowTemplate.Height = 29;
            this.dgvMeasureData.SelectedIndex = -1;
            this.dgvMeasureData.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvMeasureData.Size = new System.Drawing.Size(1194, 409);
            this.dgvMeasureData.TabIndex = 9;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.dgvCalibrationData);
            this.tabPage2.Location = new System.Drawing.Point(0, 40);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Size = new System.Drawing.Size(1194, 409);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "校准数据";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // dgvCalibrationData
            // 
            this.dgvCalibrationData.AllowUserToAddRows = false;
            this.dgvCalibrationData.AllowUserToDeleteRows = false;
            this.dgvCalibrationData.AllowUserToResizeRows = false;
            dataGridViewCellStyle21.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle21.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.dgvCalibrationData.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle21;
            this.dgvCalibrationData.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvCalibrationData.AutoSizeRowsMode = System.Windows.Forms.DataGridViewAutoSizeRowsMode.AllCells;
            this.dgvCalibrationData.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.dgvCalibrationData.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle22.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle22.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle22.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle22.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle22.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle22.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle22.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvCalibrationData.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle22;
            this.dgvCalibrationData.ColumnHeadersHeight = 32;
            this.dgvCalibrationData.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            dataGridViewCellStyle23.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle23.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle23.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle23.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle23.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(236)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle23.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle23.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvCalibrationData.DefaultCellStyle = dataGridViewCellStyle23;
            this.dgvCalibrationData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvCalibrationData.EnableHeadersVisualStyles = false;
            this.dgvCalibrationData.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.dgvCalibrationData.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(173)))), ((int)(((byte)(255)))));
            this.dgvCalibrationData.Location = new System.Drawing.Point(0, 0);
            this.dgvCalibrationData.Name = "dgvCalibrationData";
            this.dgvCalibrationData.RightToLeft = System.Windows.Forms.RightToLeft.No;
            dataGridViewCellStyle24.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle24.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle24.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle24.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle24.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle24.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle24.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvCalibrationData.RowHeadersDefaultCellStyle = dataGridViewCellStyle24;
            this.dgvCalibrationData.RowHeadersVisible = false;
            dataGridViewCellStyle25.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle25.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle25.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle25.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(236)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle25.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.dgvCalibrationData.RowsDefaultCellStyle = dataGridViewCellStyle25;
            this.dgvCalibrationData.RowTemplate.Height = 29;
            this.dgvCalibrationData.SelectedIndex = -1;
            this.dgvCalibrationData.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvCalibrationData.Size = new System.Drawing.Size(1194, 409);
            this.dgvCalibrationData.TabIndex = 10;
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.dgvCurveData);
            this.tabPage3.Location = new System.Drawing.Point(0, 40);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Size = new System.Drawing.Size(1194, 409);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "曲线数据";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // dgvCurveData
            // 
            this.dgvCurveData.AllowUserToAddRows = false;
            this.dgvCurveData.AllowUserToDeleteRows = false;
            this.dgvCurveData.AllowUserToResizeRows = false;
            dataGridViewCellStyle26.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle26.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.dgvCurveData.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle26;
            this.dgvCurveData.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvCurveData.AutoSizeRowsMode = System.Windows.Forms.DataGridViewAutoSizeRowsMode.AllCells;
            this.dgvCurveData.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.dgvCurveData.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle27.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle27.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle27.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle27.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle27.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle27.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle27.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvCurveData.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle27;
            this.dgvCurveData.ColumnHeadersHeight = 32;
            this.dgvCurveData.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            dataGridViewCellStyle28.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle28.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle28.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle28.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle28.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(236)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle28.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle28.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvCurveData.DefaultCellStyle = dataGridViewCellStyle28;
            this.dgvCurveData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvCurveData.EnableHeadersVisualStyles = false;
            this.dgvCurveData.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.dgvCurveData.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(173)))), ((int)(((byte)(255)))));
            this.dgvCurveData.Location = new System.Drawing.Point(0, 0);
            this.dgvCurveData.Name = "dgvCurveData";
            this.dgvCurveData.RightToLeft = System.Windows.Forms.RightToLeft.No;
            dataGridViewCellStyle29.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle29.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle29.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle29.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle29.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle29.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle29.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvCurveData.RowHeadersDefaultCellStyle = dataGridViewCellStyle29;
            this.dgvCurveData.RowHeadersVisible = false;
            dataGridViewCellStyle30.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle30.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle30.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle30.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(236)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle30.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.dgvCurveData.RowsDefaultCellStyle = dataGridViewCellStyle30;
            this.dgvCurveData.RowTemplate.Height = 29;
            this.dgvCurveData.SelectedIndex = -1;
            this.dgvCurveData.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvCurveData.Size = new System.Drawing.Size(1194, 409);
            this.dgvCurveData.TabIndex = 10;
            // 
            // pnlTop
            // 
            this.pnlTop.Controls.Add(this.uiGroupBox5);
            this.pnlTop.Controls.Add(this.uiGroupBox3);
            this.pnlTop.Controls.Add(this.uiGroupBox2);
            this.pnlTop.Controls.Add(this.uiGroupBox1);
            this.pnlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlTop.Font = new System.Drawing.Font("宋体", 12F);
            this.pnlTop.Location = new System.Drawing.Point(2, 32);
            this.pnlTop.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlTop.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlTop.Name = "pnlTop";
            this.pnlTop.Padding = new System.Windows.Forms.Padding(1, 25, 1, 1);
            this.pnlTop.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.pnlTop.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.pnlTop.ShowCollapse = true;
            this.pnlTop.ShowText = false;
            this.pnlTop.Size = new System.Drawing.Size(1194, 332);
            this.pnlTop.TabIndex = 10;
            this.pnlTop.Text = "属性信息";
            this.pnlTop.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            this.pnlTop.TitleHeight = 25;
            // 
            // uiGroupBox5
            // 
            this.uiGroupBox5.Controls.Add(this.uiLabel31);
            this.uiGroupBox5.Controls.Add(this.uiLabel32);
            this.uiGroupBox5.Controls.Add(this.cmbAuditor);
            this.uiGroupBox5.Controls.Add(this.uiLabel30);
            this.uiGroupBox5.Controls.Add(this.txtTemp);
            this.uiGroupBox5.Controls.Add(this.uiLabel27);
            this.uiGroupBox5.Controls.Add(this.cmbInspector);
            this.uiGroupBox5.Controls.Add(this.uiLabel29);
            this.uiGroupBox5.Controls.Add(this.uiLabel28);
            this.uiGroupBox5.Controls.Add(this.txtHumidity);
            this.uiGroupBox5.Dock = System.Windows.Forms.DockStyle.Left;
            this.uiGroupBox5.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiGroupBox5.Location = new System.Drawing.Point(1012, 25);
            this.uiGroupBox5.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiGroupBox5.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiGroupBox5.Name = "uiGroupBox5";
            this.uiGroupBox5.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.uiGroupBox5.Size = new System.Drawing.Size(180, 306);
            this.uiGroupBox5.TabIndex = 3;
            this.uiGroupBox5.Text = "报表信息";
            this.uiGroupBox5.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiLabel31
            // 
            this.uiLabel31.AutoSize = true;
            this.uiLabel31.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel31.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel31.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel31.Location = new System.Drawing.Point(145, 81);
            this.uiLabel31.Name = "uiLabel31";
            this.uiLabel31.Size = new System.Drawing.Size(23, 16);
            this.uiLabel31.TabIndex = 26;
            this.uiLabel31.Text = "℃";
            // 
            // uiLabel32
            // 
            this.uiLabel32.AutoSize = true;
            this.uiLabel32.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel32.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel32.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel32.Location = new System.Drawing.Point(149, 133);
            this.uiLabel32.Name = "uiLabel32";
            this.uiLabel32.Size = new System.Drawing.Size(15, 16);
            this.uiLabel32.TabIndex = 25;
            this.uiLabel32.Text = "%";
            // 
            // cmbAuditor
            // 
            this.cmbAuditor.DataSource = null;
            this.cmbAuditor.FillColor = System.Drawing.Color.White;
            this.cmbAuditor.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbAuditor.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbAuditor.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbAuditor.Location = new System.Drawing.Point(60, 231);
            this.cmbAuditor.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbAuditor.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbAuditor.Name = "cmbAuditor";
            this.cmbAuditor.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbAuditor.Size = new System.Drawing.Size(114, 29);
            this.cmbAuditor.SymbolSize = 24;
            this.cmbAuditor.TabIndex = 24;
            this.cmbAuditor.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbAuditor.Watermark = "";
            // 
            // uiLabel30
            // 
            this.uiLabel30.AutoSize = true;
            this.uiLabel30.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel30.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel30.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel30.Location = new System.Drawing.Point(5, 237);
            this.uiLabel30.Name = "uiLabel30";
            this.uiLabel30.Size = new System.Drawing.Size(55, 16);
            this.uiLabel30.TabIndex = 23;
            this.uiLabel30.Text = "审核员";
            // 
            // txtTemp
            // 
            this.txtTemp.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtTemp.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtTemp.Location = new System.Drawing.Point(62, 75);
            this.txtTemp.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtTemp.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtTemp.Name = "txtTemp";
            this.txtTemp.Padding = new System.Windows.Forms.Padding(5);
            this.txtTemp.ShowText = false;
            this.txtTemp.Size = new System.Drawing.Size(80, 29);
            this.txtTemp.TabIndex = 22;
            this.txtTemp.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtTemp.Watermark = "";
            // 
            // uiLabel27
            // 
            this.uiLabel27.AutoSize = true;
            this.uiLabel27.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel27.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel27.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel27.Location = new System.Drawing.Point(21, 81);
            this.uiLabel27.Name = "uiLabel27";
            this.uiLabel27.Size = new System.Drawing.Size(39, 16);
            this.uiLabel27.TabIndex = 23;
            this.uiLabel27.Text = "温度";
            // 
            // cmbInspector
            // 
            this.cmbInspector.DataSource = null;
            this.cmbInspector.FillColor = System.Drawing.Color.White;
            this.cmbInspector.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbInspector.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbInspector.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbInspector.Location = new System.Drawing.Point(60, 179);
            this.cmbInspector.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbInspector.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbInspector.Name = "cmbInspector";
            this.cmbInspector.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbInspector.Size = new System.Drawing.Size(114, 29);
            this.cmbInspector.SymbolSize = 24;
            this.cmbInspector.TabIndex = 22;
            this.cmbInspector.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbInspector.Watermark = "";
            // 
            // uiLabel29
            // 
            this.uiLabel29.AutoSize = true;
            this.uiLabel29.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel29.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel29.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel29.Location = new System.Drawing.Point(5, 185);
            this.uiLabel29.Name = "uiLabel29";
            this.uiLabel29.Size = new System.Drawing.Size(55, 16);
            this.uiLabel29.TabIndex = 21;
            this.uiLabel29.Text = "检验员";
            // 
            // uiLabel28
            // 
            this.uiLabel28.AutoSize = true;
            this.uiLabel28.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel28.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel28.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel28.Location = new System.Drawing.Point(21, 133);
            this.uiLabel28.Name = "uiLabel28";
            this.uiLabel28.Size = new System.Drawing.Size(39, 16);
            this.uiLabel28.TabIndex = 19;
            this.uiLabel28.Text = "湿度";
            // 
            // txtHumidity
            // 
            this.txtHumidity.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtHumidity.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtHumidity.Location = new System.Drawing.Point(62, 127);
            this.txtHumidity.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtHumidity.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtHumidity.Name = "txtHumidity";
            this.txtHumidity.Padding = new System.Windows.Forms.Padding(5);
            this.txtHumidity.ShowText = false;
            this.txtHumidity.Size = new System.Drawing.Size(80, 29);
            this.txtHumidity.TabIndex = 18;
            this.txtHumidity.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtHumidity.Watermark = "";
            // 
            // uiGroupBox3
            // 
            this.uiGroupBox3.Controls.Add(this.uiLabel20);
            this.uiGroupBox3.Controls.Add(this.txtRefV4);
            this.uiGroupBox3.Controls.Add(this.uiLabel21);
            this.uiGroupBox3.Controls.Add(this.txtRefV3);
            this.uiGroupBox3.Controls.Add(this.uiLabel22);
            this.uiGroupBox3.Controls.Add(this.txtRefV2);
            this.uiGroupBox3.Controls.Add(this.uiLabel23);
            this.uiGroupBox3.Controls.Add(this.txtRefV1);
            this.uiGroupBox3.Controls.Add(this.uiLabel24);
            this.uiGroupBox3.Controls.Add(this.txtRefIndex);
            this.uiGroupBox3.Controls.Add(this.uiLabel25);
            this.uiGroupBox3.Controls.Add(this.uiLabel15);
            this.uiGroupBox3.Controls.Add(this.txtMainV4);
            this.uiGroupBox3.Controls.Add(this.uiLabel13);
            this.uiGroupBox3.Controls.Add(this.txtMainV3);
            this.uiGroupBox3.Controls.Add(this.uiLabel14);
            this.uiGroupBox3.Controls.Add(this.txtMainV2);
            this.uiGroupBox3.Controls.Add(this.uiLabel7);
            this.uiGroupBox3.Controls.Add(this.txtMainV1);
            this.uiGroupBox3.Controls.Add(this.uiLabel8);
            this.uiGroupBox3.Controls.Add(this.txtMainIndex);
            this.uiGroupBox3.Controls.Add(this.uiLabel9);
            this.uiGroupBox3.Dock = System.Windows.Forms.DockStyle.Left;
            this.uiGroupBox3.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiGroupBox3.Location = new System.Drawing.Point(668, 25);
            this.uiGroupBox3.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiGroupBox3.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiGroupBox3.Name = "uiGroupBox3";
            this.uiGroupBox3.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.uiGroupBox3.Size = new System.Drawing.Size(344, 306);
            this.uiGroupBox3.TabIndex = 2;
            this.uiGroupBox3.Text = "光源信息";
            this.uiGroupBox3.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiLabel20
            // 
            this.uiLabel20.AutoSize = true;
            this.uiLabel20.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel20.Font = new System.Drawing.Font("宋体", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel20.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel20.Location = new System.Drawing.Point(198, 56);
            this.uiLabel20.Name = "uiLabel20";
            this.uiLabel20.Size = new System.Drawing.Size(129, 19);
            this.uiLabel20.TabIndex = 11;
            this.uiLabel20.Text = "参比光路信号";
            // 
            // txtRefV4
            // 
            this.txtRefV4.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtRefV4.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtRefV4.Location = new System.Drawing.Point(267, 265);
            this.txtRefV4.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtRefV4.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtRefV4.Name = "txtRefV4";
            this.txtRefV4.Padding = new System.Windows.Forms.Padding(5);
            this.txtRefV4.ReadOnly = true;
            this.txtRefV4.ShowText = false;
            this.txtRefV4.Size = new System.Drawing.Size(69, 29);
            this.txtRefV4.TabIndex = 9;
            this.txtRefV4.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtRefV4.Watermark = "";
            // 
            // uiLabel21
            // 
            this.uiLabel21.AutoSize = true;
            this.uiLabel21.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel21.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel21.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel21.Location = new System.Drawing.Point(183, 271);
            this.uiLabel21.Name = "uiLabel21";
            this.uiLabel21.Size = new System.Drawing.Size(79, 16);
            this.uiLabel21.TabIndex = 21;
            this.uiLabel21.Text = "信号电压4";
            // 
            // txtRefV3
            // 
            this.txtRefV3.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtRefV3.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtRefV3.Location = new System.Drawing.Point(267, 226);
            this.txtRefV3.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtRefV3.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtRefV3.Name = "txtRefV3";
            this.txtRefV3.Padding = new System.Windows.Forms.Padding(5);
            this.txtRefV3.ReadOnly = true;
            this.txtRefV3.ShowText = false;
            this.txtRefV3.Size = new System.Drawing.Size(69, 29);
            this.txtRefV3.TabIndex = 8;
            this.txtRefV3.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtRefV3.Watermark = "";
            // 
            // uiLabel22
            // 
            this.uiLabel22.AutoSize = true;
            this.uiLabel22.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel22.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel22.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel22.Location = new System.Drawing.Point(183, 232);
            this.uiLabel22.Name = "uiLabel22";
            this.uiLabel22.Size = new System.Drawing.Size(79, 16);
            this.uiLabel22.TabIndex = 20;
            this.uiLabel22.Text = "信号电压3";
            // 
            // txtRefV2
            // 
            this.txtRefV2.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtRefV2.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtRefV2.Location = new System.Drawing.Point(267, 187);
            this.txtRefV2.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtRefV2.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtRefV2.Name = "txtRefV2";
            this.txtRefV2.Padding = new System.Windows.Forms.Padding(5);
            this.txtRefV2.ReadOnly = true;
            this.txtRefV2.ShowText = false;
            this.txtRefV2.Size = new System.Drawing.Size(69, 29);
            this.txtRefV2.TabIndex = 7;
            this.txtRefV2.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtRefV2.Watermark = "";
            // 
            // uiLabel23
            // 
            this.uiLabel23.AutoSize = true;
            this.uiLabel23.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel23.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel23.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel23.Location = new System.Drawing.Point(183, 193);
            this.uiLabel23.Name = "uiLabel23";
            this.uiLabel23.Size = new System.Drawing.Size(79, 16);
            this.uiLabel23.TabIndex = 19;
            this.uiLabel23.Text = "信号电压2";
            // 
            // txtRefV1
            // 
            this.txtRefV1.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtRefV1.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtRefV1.Location = new System.Drawing.Point(267, 148);
            this.txtRefV1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtRefV1.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtRefV1.Name = "txtRefV1";
            this.txtRefV1.Padding = new System.Windows.Forms.Padding(5);
            this.txtRefV1.ReadOnly = true;
            this.txtRefV1.ShowText = false;
            this.txtRefV1.Size = new System.Drawing.Size(69, 29);
            this.txtRefV1.TabIndex = 6;
            this.txtRefV1.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtRefV1.Watermark = "";
            // 
            // uiLabel24
            // 
            this.uiLabel24.AutoSize = true;
            this.uiLabel24.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel24.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel24.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel24.Location = new System.Drawing.Point(183, 154);
            this.uiLabel24.Name = "uiLabel24";
            this.uiLabel24.Size = new System.Drawing.Size(79, 16);
            this.uiLabel24.TabIndex = 18;
            this.uiLabel24.Text = "信号电压1";
            // 
            // txtRefIndex
            // 
            this.txtRefIndex.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtRefIndex.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtRefIndex.Location = new System.Drawing.Point(267, 109);
            this.txtRefIndex.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtRefIndex.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtRefIndex.Name = "txtRefIndex";
            this.txtRefIndex.Padding = new System.Windows.Forms.Padding(5);
            this.txtRefIndex.ReadOnly = true;
            this.txtRefIndex.ShowText = false;
            this.txtRefIndex.Size = new System.Drawing.Size(69, 29);
            this.txtRefIndex.TabIndex = 5;
            this.txtRefIndex.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtRefIndex.Watermark = "";
            // 
            // uiLabel25
            // 
            this.uiLabel25.AutoSize = true;
            this.uiLabel25.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel25.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel25.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel25.Location = new System.Drawing.Point(191, 115);
            this.uiLabel25.Name = "uiLabel25";
            this.uiLabel25.Size = new System.Drawing.Size(71, 16);
            this.uiLabel25.TabIndex = 17;
            this.uiLabel25.Text = "信号档位";
            // 
            // uiLabel15
            // 
            this.uiLabel15.AutoSize = true;
            this.uiLabel15.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel15.Font = new System.Drawing.Font("宋体", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel15.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel15.Location = new System.Drawing.Point(24, 56);
            this.uiLabel15.Name = "uiLabel15";
            this.uiLabel15.Size = new System.Drawing.Size(129, 19);
            this.uiLabel15.TabIndex = 10;
            this.uiLabel15.Text = "检测光路信号";
            // 
            // txtMainV4
            // 
            this.txtMainV4.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtMainV4.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtMainV4.Location = new System.Drawing.Point(93, 265);
            this.txtMainV4.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtMainV4.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtMainV4.Name = "txtMainV4";
            this.txtMainV4.Padding = new System.Windows.Forms.Padding(5);
            this.txtMainV4.ReadOnly = true;
            this.txtMainV4.ShowText = false;
            this.txtMainV4.Size = new System.Drawing.Size(69, 29);
            this.txtMainV4.TabIndex = 4;
            this.txtMainV4.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtMainV4.Watermark = "";
            // 
            // uiLabel13
            // 
            this.uiLabel13.AutoSize = true;
            this.uiLabel13.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel13.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel13.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel13.Location = new System.Drawing.Point(9, 271);
            this.uiLabel13.Name = "uiLabel13";
            this.uiLabel13.Size = new System.Drawing.Size(79, 16);
            this.uiLabel13.TabIndex = 16;
            this.uiLabel13.Text = "信号电压4";
            // 
            // txtMainV3
            // 
            this.txtMainV3.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtMainV3.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtMainV3.Location = new System.Drawing.Point(93, 226);
            this.txtMainV3.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtMainV3.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtMainV3.Name = "txtMainV3";
            this.txtMainV3.Padding = new System.Windows.Forms.Padding(5);
            this.txtMainV3.ReadOnly = true;
            this.txtMainV3.ShowText = false;
            this.txtMainV3.Size = new System.Drawing.Size(69, 29);
            this.txtMainV3.TabIndex = 3;
            this.txtMainV3.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtMainV3.Watermark = "";
            // 
            // uiLabel14
            // 
            this.uiLabel14.AutoSize = true;
            this.uiLabel14.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel14.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel14.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel14.Location = new System.Drawing.Point(9, 232);
            this.uiLabel14.Name = "uiLabel14";
            this.uiLabel14.Size = new System.Drawing.Size(79, 16);
            this.uiLabel14.TabIndex = 15;
            this.uiLabel14.Text = "信号电压3";
            // 
            // txtMainV2
            // 
            this.txtMainV2.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtMainV2.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtMainV2.Location = new System.Drawing.Point(93, 187);
            this.txtMainV2.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtMainV2.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtMainV2.Name = "txtMainV2";
            this.txtMainV2.Padding = new System.Windows.Forms.Padding(5);
            this.txtMainV2.ReadOnly = true;
            this.txtMainV2.ShowText = false;
            this.txtMainV2.Size = new System.Drawing.Size(69, 29);
            this.txtMainV2.TabIndex = 2;
            this.txtMainV2.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtMainV2.Watermark = "";
            // 
            // uiLabel7
            // 
            this.uiLabel7.AutoSize = true;
            this.uiLabel7.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel7.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel7.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel7.Location = new System.Drawing.Point(9, 193);
            this.uiLabel7.Name = "uiLabel7";
            this.uiLabel7.Size = new System.Drawing.Size(79, 16);
            this.uiLabel7.TabIndex = 14;
            this.uiLabel7.Text = "信号电压2";
            // 
            // txtMainV1
            // 
            this.txtMainV1.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtMainV1.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtMainV1.Location = new System.Drawing.Point(93, 148);
            this.txtMainV1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtMainV1.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtMainV1.Name = "txtMainV1";
            this.txtMainV1.Padding = new System.Windows.Forms.Padding(5);
            this.txtMainV1.ReadOnly = true;
            this.txtMainV1.ShowText = false;
            this.txtMainV1.Size = new System.Drawing.Size(69, 29);
            this.txtMainV1.TabIndex = 1;
            this.txtMainV1.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtMainV1.Watermark = "";
            // 
            // uiLabel8
            // 
            this.uiLabel8.AutoSize = true;
            this.uiLabel8.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel8.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel8.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel8.Location = new System.Drawing.Point(9, 154);
            this.uiLabel8.Name = "uiLabel8";
            this.uiLabel8.Size = new System.Drawing.Size(79, 16);
            this.uiLabel8.TabIndex = 13;
            this.uiLabel8.Text = "信号电压1";
            // 
            // txtMainIndex
            // 
            this.txtMainIndex.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtMainIndex.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtMainIndex.Location = new System.Drawing.Point(93, 109);
            this.txtMainIndex.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtMainIndex.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtMainIndex.Name = "txtMainIndex";
            this.txtMainIndex.Padding = new System.Windows.Forms.Padding(5);
            this.txtMainIndex.ReadOnly = true;
            this.txtMainIndex.ShowText = false;
            this.txtMainIndex.Size = new System.Drawing.Size(69, 29);
            this.txtMainIndex.TabIndex = 0;
            this.txtMainIndex.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtMainIndex.Watermark = "";
            // 
            // uiLabel9
            // 
            this.uiLabel9.AutoSize = true;
            this.uiLabel9.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel9.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel9.Location = new System.Drawing.Point(17, 115);
            this.uiLabel9.Name = "uiLabel9";
            this.uiLabel9.Size = new System.Drawing.Size(71, 16);
            this.uiLabel9.TabIndex = 12;
            this.uiLabel9.Text = "信号档位";
            // 
            // uiGroupBox2
            // 
            this.uiGroupBox2.Controls.Add(this.txtValveSN);
            this.uiGroupBox2.Controls.Add(this.uiLabel19);
            this.uiGroupBox2.Controls.Add(this.txtValveVersion);
            this.uiGroupBox2.Controls.Add(this.uiLabel16);
            this.uiGroupBox2.Controls.Add(this.txtPumpSN);
            this.uiGroupBox2.Controls.Add(this.uiLabel17);
            this.uiGroupBox2.Controls.Add(this.txtPumpVersion);
            this.uiGroupBox2.Controls.Add(this.uiLabel18);
            this.uiGroupBox2.Controls.Add(this.txtFlowVersion);
            this.uiGroupBox2.Controls.Add(this.uiLabel10);
            this.uiGroupBox2.Controls.Add(this.txtHmiVersion);
            this.uiGroupBox2.Controls.Add(this.uiLabel11);
            this.uiGroupBox2.Controls.Add(this.txtMainBoardVersion);
            this.uiGroupBox2.Controls.Add(this.uiLabel12);
            this.uiGroupBox2.Dock = System.Windows.Forms.DockStyle.Left;
            this.uiGroupBox2.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiGroupBox2.Location = new System.Drawing.Point(313, 25);
            this.uiGroupBox2.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiGroupBox2.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiGroupBox2.Name = "uiGroupBox2";
            this.uiGroupBox2.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.uiGroupBox2.Size = new System.Drawing.Size(355, 306);
            this.uiGroupBox2.TabIndex = 1;
            this.uiGroupBox2.Text = "软件版本信息";
            this.uiGroupBox2.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // txtValveSN
            // 
            this.txtValveSN.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtValveSN.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtValveSN.Location = new System.Drawing.Point(126, 265);
            this.txtValveSN.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtValveSN.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtValveSN.Name = "txtValveSN";
            this.txtValveSN.Padding = new System.Windows.Forms.Padding(5);
            this.txtValveSN.ReadOnly = true;
            this.txtValveSN.ShowText = false;
            this.txtValveSN.Size = new System.Drawing.Size(217, 29);
            this.txtValveSN.TabIndex = 6;
            this.txtValveSN.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtValveSN.Watermark = "";
            // 
            // uiLabel19
            // 
            this.uiLabel19.AutoSize = true;
            this.uiLabel19.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel19.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel19.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel19.Location = new System.Drawing.Point(23, 271);
            this.uiLabel19.Name = "uiLabel19";
            this.uiLabel19.Size = new System.Drawing.Size(103, 16);
            this.uiLabel19.TabIndex = 13;
            this.uiLabel19.Text = "选向阀序列号";
            // 
            // txtValveVersion
            // 
            this.txtValveVersion.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtValveVersion.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtValveVersion.Location = new System.Drawing.Point(126, 226);
            this.txtValveVersion.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtValveVersion.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtValveVersion.Name = "txtValveVersion";
            this.txtValveVersion.Padding = new System.Windows.Forms.Padding(5);
            this.txtValveVersion.ReadOnly = true;
            this.txtValveVersion.ShowText = false;
            this.txtValveVersion.Size = new System.Drawing.Size(217, 29);
            this.txtValveVersion.TabIndex = 5;
            this.txtValveVersion.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtValveVersion.Watermark = "";
            // 
            // uiLabel16
            // 
            this.uiLabel16.AutoSize = true;
            this.uiLabel16.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel16.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel16.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel16.Location = new System.Drawing.Point(7, 232);
            this.uiLabel16.Name = "uiLabel16";
            this.uiLabel16.Size = new System.Drawing.Size(119, 16);
            this.uiLabel16.TabIndex = 12;
            this.uiLabel16.Text = "选向阀软件版本";
            // 
            // txtPumpSN
            // 
            this.txtPumpSN.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtPumpSN.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtPumpSN.Location = new System.Drawing.Point(126, 187);
            this.txtPumpSN.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtPumpSN.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtPumpSN.Name = "txtPumpSN";
            this.txtPumpSN.Padding = new System.Windows.Forms.Padding(5);
            this.txtPumpSN.ReadOnly = true;
            this.txtPumpSN.ShowText = false;
            this.txtPumpSN.Size = new System.Drawing.Size(217, 29);
            this.txtPumpSN.TabIndex = 4;
            this.txtPumpSN.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtPumpSN.Watermark = "";
            // 
            // uiLabel17
            // 
            this.uiLabel17.AutoSize = true;
            this.uiLabel17.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel17.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel17.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel17.Location = new System.Drawing.Point(23, 193);
            this.uiLabel17.Name = "uiLabel17";
            this.uiLabel17.Size = new System.Drawing.Size(103, 16);
            this.uiLabel17.TabIndex = 11;
            this.uiLabel17.Text = "柱塞泵序列号";
            // 
            // txtPumpVersion
            // 
            this.txtPumpVersion.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtPumpVersion.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtPumpVersion.Location = new System.Drawing.Point(126, 148);
            this.txtPumpVersion.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtPumpVersion.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtPumpVersion.Name = "txtPumpVersion";
            this.txtPumpVersion.Padding = new System.Windows.Forms.Padding(5);
            this.txtPumpVersion.ReadOnly = true;
            this.txtPumpVersion.ShowText = false;
            this.txtPumpVersion.Size = new System.Drawing.Size(217, 29);
            this.txtPumpVersion.TabIndex = 3;
            this.txtPumpVersion.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtPumpVersion.Watermark = "";
            // 
            // uiLabel18
            // 
            this.uiLabel18.AutoSize = true;
            this.uiLabel18.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel18.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel18.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel18.Location = new System.Drawing.Point(7, 154);
            this.uiLabel18.Name = "uiLabel18";
            this.uiLabel18.Size = new System.Drawing.Size(119, 16);
            this.uiLabel18.TabIndex = 10;
            this.uiLabel18.Text = "柱塞泵软件版本";
            // 
            // txtFlowVersion
            // 
            this.txtFlowVersion.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtFlowVersion.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtFlowVersion.Location = new System.Drawing.Point(126, 109);
            this.txtFlowVersion.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtFlowVersion.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtFlowVersion.Name = "txtFlowVersion";
            this.txtFlowVersion.Padding = new System.Windows.Forms.Padding(5);
            this.txtFlowVersion.ReadOnly = true;
            this.txtFlowVersion.ShowText = false;
            this.txtFlowVersion.Size = new System.Drawing.Size(217, 29);
            this.txtFlowVersion.TabIndex = 2;
            this.txtFlowVersion.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtFlowVersion.Watermark = "";
            // 
            // uiLabel10
            // 
            this.uiLabel10.AutoSize = true;
            this.uiLabel10.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel10.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel10.Location = new System.Drawing.Point(55, 115);
            this.uiLabel10.Name = "uiLabel10";
            this.uiLabel10.Size = new System.Drawing.Size(71, 16);
            this.uiLabel10.TabIndex = 9;
            this.uiLabel10.Text = "流路版本";
            // 
            // txtHmiVersion
            // 
            this.txtHmiVersion.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtHmiVersion.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtHmiVersion.Location = new System.Drawing.Point(126, 70);
            this.txtHmiVersion.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtHmiVersion.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtHmiVersion.Name = "txtHmiVersion";
            this.txtHmiVersion.Padding = new System.Windows.Forms.Padding(5);
            this.txtHmiVersion.ReadOnly = true;
            this.txtHmiVersion.ShowText = false;
            this.txtHmiVersion.Size = new System.Drawing.Size(217, 29);
            this.txtHmiVersion.TabIndex = 1;
            this.txtHmiVersion.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtHmiVersion.Watermark = "";
            // 
            // uiLabel11
            // 
            this.uiLabel11.AutoSize = true;
            this.uiLabel11.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel11.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel11.Location = new System.Drawing.Point(23, 76);
            this.uiLabel11.Name = "uiLabel11";
            this.uiLabel11.Size = new System.Drawing.Size(103, 16);
            this.uiLabel11.TabIndex = 8;
            this.uiLabel11.Text = "屏幕软件版本";
            // 
            // txtMainBoardVersion
            // 
            this.txtMainBoardVersion.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtMainBoardVersion.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtMainBoardVersion.Location = new System.Drawing.Point(126, 31);
            this.txtMainBoardVersion.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtMainBoardVersion.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtMainBoardVersion.Name = "txtMainBoardVersion";
            this.txtMainBoardVersion.Padding = new System.Windows.Forms.Padding(5);
            this.txtMainBoardVersion.ReadOnly = true;
            this.txtMainBoardVersion.ShowText = false;
            this.txtMainBoardVersion.Size = new System.Drawing.Size(217, 29);
            this.txtMainBoardVersion.TabIndex = 0;
            this.txtMainBoardVersion.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtMainBoardVersion.Watermark = "";
            // 
            // uiLabel12
            // 
            this.uiLabel12.AutoSize = true;
            this.uiLabel12.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel12.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel12.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel12.Location = new System.Drawing.Point(23, 37);
            this.uiLabel12.Name = "uiLabel12";
            this.uiLabel12.Size = new System.Drawing.Size(103, 16);
            this.uiLabel12.TabIndex = 7;
            this.uiLabel12.Text = "主板软件版本";
            // 
            // uiGroupBox1
            // 
            this.uiGroupBox1.Controls.Add(this.txtSNCode);
            this.uiGroupBox1.Controls.Add(this.uiLabel26);
            this.uiGroupBox1.Controls.Add(this.lblAuxiliaryRangeUnit);
            this.uiGroupBox1.Controls.Add(this.lblStandardRangeUnit);
            this.uiGroupBox1.Controls.Add(this.txtAuxiliaryRange);
            this.uiGroupBox1.Controls.Add(this.uiLabel6);
            this.uiGroupBox1.Controls.Add(this.txtStandardRange);
            this.uiGroupBox1.Controls.Add(this.uiLabel5);
            this.uiGroupBox1.Controls.Add(this.txtInspectionBasis);
            this.uiGroupBox1.Controls.Add(this.uiLabel4);
            this.uiGroupBox1.Controls.Add(this.txtIdCode);
            this.uiGroupBox1.Controls.Add(this.uiLabel3);
            this.uiGroupBox1.Controls.Add(this.txtModel);
            this.uiGroupBox1.Controls.Add(this.uiLabel2);
            this.uiGroupBox1.Controls.Add(this.txtFactor);
            this.uiGroupBox1.Controls.Add(this.uiLabel1);
            this.uiGroupBox1.Dock = System.Windows.Forms.DockStyle.Left;
            this.uiGroupBox1.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiGroupBox1.Location = new System.Drawing.Point(1, 25);
            this.uiGroupBox1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiGroupBox1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiGroupBox1.Name = "uiGroupBox1";
            this.uiGroupBox1.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.uiGroupBox1.Size = new System.Drawing.Size(312, 306);
            this.uiGroupBox1.TabIndex = 0;
            this.uiGroupBox1.Text = "因子基础信息";
            this.uiGroupBox1.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // txtSNCode
            // 
            this.txtSNCode.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtSNCode.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtSNCode.Location = new System.Drawing.Point(110, 31);
            this.txtSNCode.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtSNCode.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtSNCode.Name = "txtSNCode";
            this.txtSNCode.Padding = new System.Windows.Forms.Padding(5);
            this.txtSNCode.ReadOnly = true;
            this.txtSNCode.ShowText = false;
            this.txtSNCode.Size = new System.Drawing.Size(150, 29);
            this.txtSNCode.TabIndex = 14;
            this.txtSNCode.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtSNCode.Watermark = "";
            // 
            // uiLabel26
            // 
            this.uiLabel26.AutoSize = true;
            this.uiLabel26.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel26.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel26.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel26.Location = new System.Drawing.Point(71, 37);
            this.uiLabel26.Name = "uiLabel26";
            this.uiLabel26.Size = new System.Drawing.Size(39, 16);
            this.uiLabel26.TabIndex = 15;
            this.uiLabel26.Text = "SN号";
            // 
            // lblAuxiliaryRangeUnit
            // 
            this.lblAuxiliaryRangeUnit.AutoSize = true;
            this.lblAuxiliaryRangeUnit.BackColor = System.Drawing.Color.Transparent;
            this.lblAuxiliaryRangeUnit.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblAuxiliaryRangeUnit.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblAuxiliaryRangeUnit.Location = new System.Drawing.Point(266, 271);
            this.lblAuxiliaryRangeUnit.Name = "lblAuxiliaryRangeUnit";
            this.lblAuxiliaryRangeUnit.Size = new System.Drawing.Size(39, 16);
            this.lblAuxiliaryRangeUnit.TabIndex = 13;
            this.lblAuxiliaryRangeUnit.Text = "单位";
            // 
            // lblStandardRangeUnit
            // 
            this.lblStandardRangeUnit.AutoSize = true;
            this.lblStandardRangeUnit.BackColor = System.Drawing.Color.Transparent;
            this.lblStandardRangeUnit.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblStandardRangeUnit.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblStandardRangeUnit.Location = new System.Drawing.Point(266, 232);
            this.lblStandardRangeUnit.Name = "lblStandardRangeUnit";
            this.lblStandardRangeUnit.Size = new System.Drawing.Size(39, 16);
            this.lblStandardRangeUnit.TabIndex = 12;
            this.lblStandardRangeUnit.Text = "单位";
            // 
            // txtAuxiliaryRange
            // 
            this.txtAuxiliaryRange.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtAuxiliaryRange.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtAuxiliaryRange.Location = new System.Drawing.Point(110, 265);
            this.txtAuxiliaryRange.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtAuxiliaryRange.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtAuxiliaryRange.Name = "txtAuxiliaryRange";
            this.txtAuxiliaryRange.Padding = new System.Windows.Forms.Padding(5);
            this.txtAuxiliaryRange.ReadOnly = true;
            this.txtAuxiliaryRange.ShowText = false;
            this.txtAuxiliaryRange.Size = new System.Drawing.Size(150, 29);
            this.txtAuxiliaryRange.TabIndex = 5;
            this.txtAuxiliaryRange.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtAuxiliaryRange.Watermark = "";
            // 
            // uiLabel6
            // 
            this.uiLabel6.AutoSize = true;
            this.uiLabel6.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel6.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel6.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel6.Location = new System.Drawing.Point(7, 271);
            this.uiLabel6.Name = "uiLabel6";
            this.uiLabel6.Size = new System.Drawing.Size(103, 16);
            this.uiLabel6.TabIndex = 11;
            this.uiLabel6.Text = "测试辅助量程";
            // 
            // txtStandardRange
            // 
            this.txtStandardRange.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtStandardRange.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtStandardRange.Location = new System.Drawing.Point(110, 226);
            this.txtStandardRange.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtStandardRange.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtStandardRange.Name = "txtStandardRange";
            this.txtStandardRange.Padding = new System.Windows.Forms.Padding(5);
            this.txtStandardRange.ReadOnly = true;
            this.txtStandardRange.ShowText = false;
            this.txtStandardRange.Size = new System.Drawing.Size(150, 29);
            this.txtStandardRange.TabIndex = 4;
            this.txtStandardRange.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtStandardRange.Watermark = "";
            // 
            // uiLabel5
            // 
            this.uiLabel5.AutoSize = true;
            this.uiLabel5.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel5.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel5.Location = new System.Drawing.Point(7, 232);
            this.uiLabel5.Name = "uiLabel5";
            this.uiLabel5.Size = new System.Drawing.Size(103, 16);
            this.uiLabel5.TabIndex = 10;
            this.uiLabel5.Text = "测试标准量程";
            // 
            // txtInspectionBasis
            // 
            this.txtInspectionBasis.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtInspectionBasis.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtInspectionBasis.Location = new System.Drawing.Point(110, 187);
            this.txtInspectionBasis.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtInspectionBasis.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtInspectionBasis.Name = "txtInspectionBasis";
            this.txtInspectionBasis.Padding = new System.Windows.Forms.Padding(5);
            this.txtInspectionBasis.ReadOnly = true;
            this.txtInspectionBasis.ShowText = false;
            this.txtInspectionBasis.Size = new System.Drawing.Size(150, 29);
            this.txtInspectionBasis.TabIndex = 3;
            this.txtInspectionBasis.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtInspectionBasis.Watermark = "";
            // 
            // uiLabel4
            // 
            this.uiLabel4.AutoSize = true;
            this.uiLabel4.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel4.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel4.Location = new System.Drawing.Point(39, 193);
            this.uiLabel4.Name = "uiLabel4";
            this.uiLabel4.Size = new System.Drawing.Size(71, 16);
            this.uiLabel4.TabIndex = 9;
            this.uiLabel4.Text = "检验依据";
            // 
            // txtIdCode
            // 
            this.txtIdCode.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtIdCode.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtIdCode.Location = new System.Drawing.Point(110, 148);
            this.txtIdCode.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtIdCode.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtIdCode.Name = "txtIdCode";
            this.txtIdCode.Padding = new System.Windows.Forms.Padding(5);
            this.txtIdCode.ReadOnly = true;
            this.txtIdCode.ShowText = false;
            this.txtIdCode.Size = new System.Drawing.Size(150, 29);
            this.txtIdCode.TabIndex = 2;
            this.txtIdCode.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtIdCode.Watermark = "";
            // 
            // uiLabel3
            // 
            this.uiLabel3.AutoSize = true;
            this.uiLabel3.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel3.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel3.Location = new System.Drawing.Point(39, 154);
            this.uiLabel3.Name = "uiLabel3";
            this.uiLabel3.Size = new System.Drawing.Size(71, 16);
            this.uiLabel3.TabIndex = 8;
            this.uiLabel3.Text = "ID识别码";
            // 
            // txtModel
            // 
            this.txtModel.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtModel.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtModel.Location = new System.Drawing.Point(110, 109);
            this.txtModel.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtModel.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtModel.Name = "txtModel";
            this.txtModel.Padding = new System.Windows.Forms.Padding(5);
            this.txtModel.ReadOnly = true;
            this.txtModel.ShowText = false;
            this.txtModel.Size = new System.Drawing.Size(150, 29);
            this.txtModel.TabIndex = 1;
            this.txtModel.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtModel.Watermark = "";
            // 
            // uiLabel2
            // 
            this.uiLabel2.AutoSize = true;
            this.uiLabel2.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel2.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel2.Location = new System.Drawing.Point(39, 115);
            this.uiLabel2.Name = "uiLabel2";
            this.uiLabel2.Size = new System.Drawing.Size(71, 16);
            this.uiLabel2.TabIndex = 7;
            this.uiLabel2.Text = "设备型号";
            // 
            // txtFactor
            // 
            this.txtFactor.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtFactor.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtFactor.Location = new System.Drawing.Point(110, 70);
            this.txtFactor.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtFactor.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtFactor.Name = "txtFactor";
            this.txtFactor.Padding = new System.Windows.Forms.Padding(5);
            this.txtFactor.ReadOnly = true;
            this.txtFactor.ShowText = false;
            this.txtFactor.Size = new System.Drawing.Size(150, 29);
            this.txtFactor.TabIndex = 0;
            this.txtFactor.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtFactor.Watermark = "";
            // 
            // uiLabel1
            // 
            this.uiLabel1.AutoSize = true;
            this.uiLabel1.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel1.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel1.Location = new System.Drawing.Point(39, 76);
            this.uiLabel1.Name = "uiLabel1";
            this.uiLabel1.Size = new System.Drawing.Size(71, 16);
            this.uiLabel1.TabIndex = 6;
            this.uiLabel1.Text = "因子类型";
            // 
            // UC_ReportDataShow
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.gbDataDetail);
            this.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "UC_ReportDataShow";
            this.Padding = new System.Windows.Forms.Padding(1);
            this.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.Size = new System.Drawing.Size(1200, 817);
            this.Text = "报表数据展示";
            this.Load += new System.EventHandler(this.UC_ReportDataShow_Load);
            this.gbDataDetail.ResumeLayout(false);
            this.tabMain.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvMeasureData)).EndInit();
            this.tabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvCalibrationData)).EndInit();
            this.tabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvCurveData)).EndInit();
            this.pnlTop.ResumeLayout(false);
            this.uiGroupBox5.ResumeLayout(false);
            this.uiGroupBox5.PerformLayout();
            this.uiGroupBox3.ResumeLayout(false);
            this.uiGroupBox3.PerformLayout();
            this.uiGroupBox2.ResumeLayout(false);
            this.uiGroupBox2.PerformLayout();
            this.uiGroupBox1.ResumeLayout(false);
            this.uiGroupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
        private Sunny.UI.UIGroupBox gbDataDetail;
        private Sunny.UI.UIGroupBox uiGroupBox3;
        private Sunny.UI.UILabel uiLabel15;
        private Sunny.UI.UITextBox txtMainV4;
        private Sunny.UI.UILabel uiLabel13;
        private Sunny.UI.UITextBox txtMainV3;
        private Sunny.UI.UILabel uiLabel14;
        private Sunny.UI.UITextBox txtMainV2;
        private Sunny.UI.UILabel uiLabel7;
        private Sunny.UI.UITextBox txtMainV1;
        private Sunny.UI.UILabel uiLabel8;
        private Sunny.UI.UITextBox txtMainIndex;
        private Sunny.UI.UILabel uiLabel9;
        private Sunny.UI.UIGroupBox uiGroupBox2;
        private Sunny.UI.UITextBox txtValveSN;
        private Sunny.UI.UILabel uiLabel19;
        private Sunny.UI.UITextBox txtValveVersion;
        private Sunny.UI.UILabel uiLabel16;
        private Sunny.UI.UITextBox txtPumpSN;
        private Sunny.UI.UILabel uiLabel17;
        private Sunny.UI.UITextBox txtPumpVersion;
        private Sunny.UI.UILabel uiLabel18;
        private Sunny.UI.UITextBox txtFlowVersion;
        private Sunny.UI.UILabel uiLabel10;
        private Sunny.UI.UITextBox txtMainBoardVersion;
        private Sunny.UI.UILabel uiLabel12;
        private Sunny.UI.UIGroupBox uiGroupBox1;
        private Sunny.UI.UILabel lblAuxiliaryRangeUnit;
        private Sunny.UI.UILabel lblStandardRangeUnit;
        private Sunny.UI.UITextBox txtAuxiliaryRange;
        private Sunny.UI.UILabel uiLabel6;
        private Sunny.UI.UITextBox txtStandardRange;
        private Sunny.UI.UILabel uiLabel5;
        private Sunny.UI.UITextBox txtInspectionBasis;
        private Sunny.UI.UILabel uiLabel4;
        private Sunny.UI.UITextBox txtIdCode;
        private Sunny.UI.UILabel uiLabel3;
        private Sunny.UI.UILabel uiLabel2;
        private Sunny.UI.UITextBox txtFactor;
        private Sunny.UI.UILabel uiLabel1;
        private Sunny.UI.UITextBox txtHmiVersion;
        private Sunny.UI.UILabel uiLabel11;
        private Sunny.UI.UITextBox txtModel;
        private Sunny.UI.UILabel uiLabel20;
        private Sunny.UI.UITextBox txtRefV4;
        private Sunny.UI.UILabel uiLabel21;
        private Sunny.UI.UITextBox txtRefV3;
        private Sunny.UI.UILabel uiLabel22;
        private Sunny.UI.UITextBox txtRefV2;
        private Sunny.UI.UILabel uiLabel23;
        private Sunny.UI.UITextBox txtRefV1;
        private Sunny.UI.UILabel uiLabel24;
        private Sunny.UI.UITextBox txtRefIndex;
        private Sunny.UI.UILabel uiLabel25;
        private Sunny.UI.UITextBox txtSNCode;
        private Sunny.UI.UILabel uiLabel26;
        private Sunny.UI.UITabControl tabMain;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.TabPage tabPage3;
        protected Sunny.UI.UIDataGridView dgvMeasureData;
        protected Sunny.UI.UIDataGridView dgvCalibrationData;
        protected Sunny.UI.UIDataGridView dgvCurveData;
        private Sunny.UI.UITitlePanel pnlTop;
        private Sunny.UI.UIComboBox cmbAuditor;
        private Sunny.UI.UILabel uiLabel30;
        private Sunny.UI.UIComboBox cmbInspector;
        private Sunny.UI.UILabel uiLabel29;
        private Sunny.UI.UITextBox txtHumidity;
        private Sunny.UI.UILabel uiLabel28;
        private Sunny.UI.UIGroupBox uiGroupBox5;
        private Sunny.UI.UITextBox txtTemp;
        private Sunny.UI.UILabel uiLabel27;
        private Sunny.UI.UILabel uiLabel31;
        private Sunny.UI.UILabel uiLabel32;
    }
}
