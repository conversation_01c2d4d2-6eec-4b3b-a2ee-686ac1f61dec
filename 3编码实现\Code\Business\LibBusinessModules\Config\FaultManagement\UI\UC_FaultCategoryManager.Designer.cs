namespace LibBusinessModules.Config.FaultManagement.UI
{
    partial class UC_FaultCategoryManager
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle6 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle7 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle8 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle9 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle10 = new System.Windows.Forms.DataGridViewCellStyle();
            this.uiSplitContainer1 = new Sunny.UI.UISplitContainer();
            this.uiPanel1 = new Sunny.UI.UIPanel();
            this.dgvCategory1 = new Sunny.UI.UIDataGridView();
            this.uiPanel3 = new Sunny.UI.UIPanel();
            this.btnDeleteCategory1 = new Sunny.UI.UIButton();
            this.btnEditCategory1 = new Sunny.UI.UIButton();
            this.btnAddCategory1 = new Sunny.UI.UIButton();
            this.uiPanel2 = new Sunny.UI.UIPanel();
            this.dgvCategory2 = new Sunny.UI.UIDataGridView();
            this.Id2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Category1Id = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.CategoryName2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Description2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.uiPanel4 = new Sunny.UI.UIPanel();
            this.btnDeleteCategory2 = new Sunny.UI.UIButton();
            this.btnEditCategory2 = new Sunny.UI.UIButton();
            this.btnAddCategory2 = new Sunny.UI.UIButton();
            this.Id = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.CategoryName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Description = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.SubCount = new System.Windows.Forms.DataGridViewTextBoxColumn();
            ((System.ComponentModel.ISupportInitialize)(this.uiSplitContainer1)).BeginInit();
            this.uiSplitContainer1.Panel1.SuspendLayout();
            this.uiSplitContainer1.Panel2.SuspendLayout();
            this.uiSplitContainer1.SuspendLayout();
            this.uiPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvCategory1)).BeginInit();
            this.uiPanel3.SuspendLayout();
            this.uiPanel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvCategory2)).BeginInit();
            this.uiPanel4.SuspendLayout();
            this.SuspendLayout();
            // 
            // uiSplitContainer1
            // 
            this.uiSplitContainer1.Cursor = System.Windows.Forms.Cursors.Default;
            this.uiSplitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uiSplitContainer1.Location = new System.Drawing.Point(0, 0);
            this.uiSplitContainer1.MinimumSize = new System.Drawing.Size(20, 20);
            this.uiSplitContainer1.Name = "uiSplitContainer1";
            // 
            // uiSplitContainer1.Panel1
            // 
            this.uiSplitContainer1.Panel1.Controls.Add(this.uiPanel1);
            // 
            // uiSplitContainer1.Panel2
            // 
            this.uiSplitContainer1.Panel2.Controls.Add(this.uiPanel2);
            this.uiSplitContainer1.Size = new System.Drawing.Size(900, 600);
            this.uiSplitContainer1.SplitterDistance = 413;
            this.uiSplitContainer1.SplitterWidth = 11;
            this.uiSplitContainer1.TabIndex = 0;
            // 
            // uiPanel1
            // 
            this.uiPanel1.Controls.Add(this.dgvCategory1);
            this.uiPanel1.Controls.Add(this.uiPanel3);
            this.uiPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uiPanel1.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiPanel1.Location = new System.Drawing.Point(0, 0);
            this.uiPanel1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiPanel1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiPanel1.Name = "uiPanel1";
            this.uiPanel1.Size = new System.Drawing.Size(413, 600);
            this.uiPanel1.TabIndex = 0;
            this.uiPanel1.Text = "一级故障分类";
            this.uiPanel1.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // dgvCategory1
            // 
            this.dgvCategory1.AllowUserToAddRows = false;
            this.dgvCategory1.AllowUserToDeleteRows = false;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.dgvCategory1.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            this.dgvCategory1.BackgroundColor = System.Drawing.Color.White;
            this.dgvCategory1.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle2.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle2.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvCategory1.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            this.dgvCategory1.ColumnHeadersHeight = 32;
            this.dgvCategory1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgvCategory1.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.Id,
            this.CategoryName,
            this.Description,
            this.SubCount});
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle3.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvCategory1.DefaultCellStyle = dataGridViewCellStyle3;
            this.dgvCategory1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvCategory1.EnableHeadersVisualStyles = false;
            this.dgvCategory1.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.dgvCategory1.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            this.dgvCategory1.Location = new System.Drawing.Point(0, 0);
            this.dgvCategory1.Name = "dgvCategory1";
            this.dgvCategory1.ReadOnly = true;
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvCategory1.RowHeadersDefaultCellStyle = dataGridViewCellStyle4;
            this.dgvCategory1.RowHeadersVisible = false;
            dataGridViewCellStyle5.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle5.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.dgvCategory1.RowsDefaultCellStyle = dataGridViewCellStyle5;
            this.dgvCategory1.RowTemplate.Height = 29;
            this.dgvCategory1.SelectedIndex = -1;
            this.dgvCategory1.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvCategory1.Size = new System.Drawing.Size(413, 550);
            this.dgvCategory1.StripeOddColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.dgvCategory1.TabIndex = 1;
            this.dgvCategory1.SelectionChanged += new System.EventHandler(this.dgvCategory1_SelectionChanged);
            // 
            // uiPanel3
            // 
            this.uiPanel3.Controls.Add(this.btnDeleteCategory1);
            this.uiPanel3.Controls.Add(this.btnEditCategory1);
            this.uiPanel3.Controls.Add(this.btnAddCategory1);
            this.uiPanel3.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.uiPanel3.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiPanel3.Location = new System.Drawing.Point(0, 550);
            this.uiPanel3.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiPanel3.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiPanel3.Name = "uiPanel3";
            this.uiPanel3.Size = new System.Drawing.Size(413, 50);
            this.uiPanel3.TabIndex = 0;
            this.uiPanel3.Text = null;
            this.uiPanel3.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnDeleteCategory1
            // 
            this.btnDeleteCategory1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnDeleteCategory1.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btnDeleteCategory1.Location = new System.Drawing.Point(280, 8);
            this.btnDeleteCategory1.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnDeleteCategory1.Name = "btnDeleteCategory1";
            this.btnDeleteCategory1.Size = new System.Drawing.Size(100, 35);
            this.btnDeleteCategory1.TabIndex = 2;
            this.btnDeleteCategory1.Text = "删除";
            this.btnDeleteCategory1.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnDeleteCategory1.Click += new System.EventHandler(this.btnDeleteCategory1_Click);
            // 
            // btnEditCategory1
            // 
            this.btnEditCategory1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnEditCategory1.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btnEditCategory1.Location = new System.Drawing.Point(150, 8);
            this.btnEditCategory1.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnEditCategory1.Name = "btnEditCategory1";
            this.btnEditCategory1.Size = new System.Drawing.Size(100, 35);
            this.btnEditCategory1.TabIndex = 1;
            this.btnEditCategory1.Text = "编辑";
            this.btnEditCategory1.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnEditCategory1.Click += new System.EventHandler(this.btnEditCategory1_Click);
            // 
            // btnAddCategory1
            // 
            this.btnAddCategory1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnAddCategory1.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btnAddCategory1.Location = new System.Drawing.Point(20, 8);
            this.btnAddCategory1.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnAddCategory1.Name = "btnAddCategory1";
            this.btnAddCategory1.Size = new System.Drawing.Size(100, 35);
            this.btnAddCategory1.TabIndex = 0;
            this.btnAddCategory1.Text = "新增";
            this.btnAddCategory1.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnAddCategory1.Click += new System.EventHandler(this.btnAddCategory1_Click);
            // 
            // uiPanel2
            // 
            this.uiPanel2.Controls.Add(this.dgvCategory2);
            this.uiPanel2.Controls.Add(this.uiPanel4);
            this.uiPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uiPanel2.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiPanel2.Location = new System.Drawing.Point(0, 0);
            this.uiPanel2.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiPanel2.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiPanel2.Name = "uiPanel2";
            this.uiPanel2.Size = new System.Drawing.Size(476, 600);
            this.uiPanel2.TabIndex = 0;
            this.uiPanel2.Text = "二级故障分类";
            this.uiPanel2.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // dgvCategory2
            // 
            this.dgvCategory2.AllowUserToAddRows = false;
            this.dgvCategory2.AllowUserToDeleteRows = false;
            dataGridViewCellStyle6.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.dgvCategory2.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle6;
            this.dgvCategory2.BackgroundColor = System.Drawing.Color.White;
            this.dgvCategory2.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle7.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle7.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle7.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle7.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvCategory2.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle7;
            this.dgvCategory2.ColumnHeadersHeight = 32;
            this.dgvCategory2.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgvCategory2.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.Id2,
            this.Category1Id,
            this.CategoryName2,
            this.Description2});
            dataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle8.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle8.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle8.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle8.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle8.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle8.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvCategory2.DefaultCellStyle = dataGridViewCellStyle8;
            this.dgvCategory2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvCategory2.EnableHeadersVisualStyles = false;
            this.dgvCategory2.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.dgvCategory2.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            this.dgvCategory2.Location = new System.Drawing.Point(0, 0);
            this.dgvCategory2.Name = "dgvCategory2";
            this.dgvCategory2.ReadOnly = true;
            dataGridViewCellStyle9.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle9.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle9.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle9.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle9.SelectionForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle9.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvCategory2.RowHeadersDefaultCellStyle = dataGridViewCellStyle9;
            this.dgvCategory2.RowHeadersVisible = false;
            dataGridViewCellStyle10.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle10.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.dgvCategory2.RowsDefaultCellStyle = dataGridViewCellStyle10;
            this.dgvCategory2.RowTemplate.Height = 29;
            this.dgvCategory2.SelectedIndex = -1;
            this.dgvCategory2.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvCategory2.Size = new System.Drawing.Size(476, 550);
            this.dgvCategory2.StripeOddColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.dgvCategory2.TabIndex = 1;
            // 
            // Id2
            // 
            this.Id2.HeaderText = "ID";
            this.Id2.Name = "Id2";
            this.Id2.ReadOnly = true;
            this.Id2.Width = 60;
            // 
            // Category1Id
            // 
            this.Category1Id.HeaderText = "父分类ID";
            this.Category1Id.Name = "Category1Id";
            this.Category1Id.ReadOnly = true;
            this.Category1Id.Visible = false;
            // 
            // CategoryName2
            // 
            this.CategoryName2.HeaderText = "分类名称";
            this.CategoryName2.Name = "CategoryName2";
            this.CategoryName2.ReadOnly = true;
            this.CategoryName2.Width = 150;
            // 
            // Description2
            // 
            this.Description2.HeaderText = "描述";
            this.Description2.Name = "Description2";
            this.Description2.ReadOnly = true;
            this.Description2.Width = 280;
            // 
            // uiPanel4
            // 
            this.uiPanel4.Controls.Add(this.btnDeleteCategory2);
            this.uiPanel4.Controls.Add(this.btnEditCategory2);
            this.uiPanel4.Controls.Add(this.btnAddCategory2);
            this.uiPanel4.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.uiPanel4.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiPanel4.Location = new System.Drawing.Point(0, 550);
            this.uiPanel4.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiPanel4.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiPanel4.Name = "uiPanel4";
            this.uiPanel4.Size = new System.Drawing.Size(476, 50);
            this.uiPanel4.TabIndex = 0;
            this.uiPanel4.Text = null;
            this.uiPanel4.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnDeleteCategory2
            // 
            this.btnDeleteCategory2.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnDeleteCategory2.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btnDeleteCategory2.Location = new System.Drawing.Point(280, 8);
            this.btnDeleteCategory2.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnDeleteCategory2.Name = "btnDeleteCategory2";
            this.btnDeleteCategory2.Size = new System.Drawing.Size(100, 35);
            this.btnDeleteCategory2.TabIndex = 2;
            this.btnDeleteCategory2.Text = "删除";
            this.btnDeleteCategory2.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnDeleteCategory2.Click += new System.EventHandler(this.btnDeleteCategory2_Click);
            // 
            // btnEditCategory2
            // 
            this.btnEditCategory2.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnEditCategory2.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btnEditCategory2.Location = new System.Drawing.Point(150, 8);
            this.btnEditCategory2.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnEditCategory2.Name = "btnEditCategory2";
            this.btnEditCategory2.Size = new System.Drawing.Size(100, 35);
            this.btnEditCategory2.TabIndex = 1;
            this.btnEditCategory2.Text = "编辑";
            this.btnEditCategory2.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnEditCategory2.Click += new System.EventHandler(this.btnEditCategory2_Click);
            // 
            // btnAddCategory2
            // 
            this.btnAddCategory2.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnAddCategory2.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btnAddCategory2.Location = new System.Drawing.Point(20, 8);
            this.btnAddCategory2.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnAddCategory2.Name = "btnAddCategory2";
            this.btnAddCategory2.Size = new System.Drawing.Size(100, 35);
            this.btnAddCategory2.TabIndex = 0;
            this.btnAddCategory2.Text = "新增";
            this.btnAddCategory2.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnAddCategory2.Click += new System.EventHandler(this.btnAddCategory2_Click);
            // 
            // Id
            // 
            this.Id.HeaderText = "ID";
            this.Id.Name = "Id";
            this.Id.ReadOnly = true;
            this.Id.Width = 60;
            // 
            // CategoryName
            // 
            this.CategoryName.HeaderText = "分类名称";
            this.CategoryName.Name = "CategoryName";
            this.CategoryName.ReadOnly = true;
            this.CategoryName.Width = 120;
            // 
            // Description
            // 
            this.Description.HeaderText = "描述";
            this.Description.Name = "Description";
            this.Description.ReadOnly = true;
            this.Description.Width = 150;
            // 
            // SubCount
            // 
            this.SubCount.HeaderText = "子分类数";
            this.SubCount.Name = "SubCount";
            this.SubCount.ReadOnly = true;
            this.SubCount.Width = 90;
            // 
            // UC_FaultCategoryManager
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.uiSplitContainer1);
            this.Name = "UC_FaultCategoryManager";
            this.Size = new System.Drawing.Size(900, 600);
            this.Load += new System.EventHandler(this.UC_FaultCategoryManager_Load);
            this.uiSplitContainer1.Panel1.ResumeLayout(false);
            this.uiSplitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.uiSplitContainer1)).EndInit();
            this.uiSplitContainer1.ResumeLayout(false);
            this.uiPanel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvCategory1)).EndInit();
            this.uiPanel3.ResumeLayout(false);
            this.uiPanel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvCategory2)).EndInit();
            this.uiPanel4.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private Sunny.UI.UISplitContainer uiSplitContainer1;
        private Sunny.UI.UIPanel uiPanel1;
        private Sunny.UI.UIPanel uiPanel2;
        private Sunny.UI.UIPanel uiPanel3;
        private Sunny.UI.UIButton btnDeleteCategory1;
        private Sunny.UI.UIButton btnEditCategory1;
        private Sunny.UI.UIButton btnAddCategory1;
        private Sunny.UI.UIDataGridView dgvCategory1;
        private Sunny.UI.UIDataGridView dgvCategory2;
        private Sunny.UI.UIPanel uiPanel4;
        private Sunny.UI.UIButton btnDeleteCategory2;
        private Sunny.UI.UIButton btnEditCategory2;
        private Sunny.UI.UIButton btnAddCategory2;
        private System.Windows.Forms.DataGridViewTextBoxColumn Id2;
        private System.Windows.Forms.DataGridViewTextBoxColumn Category1Id;
        private System.Windows.Forms.DataGridViewTextBoxColumn CategoryName2;
        private System.Windows.Forms.DataGridViewTextBoxColumn Description2;
        private System.Windows.Forms.DataGridViewTextBoxColumn Id;
        private System.Windows.Forms.DataGridViewTextBoxColumn CategoryName;
        private System.Windows.Forms.DataGridViewTextBoxColumn Description;
        private System.Windows.Forms.DataGridViewTextBoxColumn SubCount;
    }
}


