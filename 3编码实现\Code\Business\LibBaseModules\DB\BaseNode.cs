﻿using LibBaseModules.Helper;
using System;
using System.Collections;
using System.ComponentModel;
using System.Reflection;
using System.Text;

namespace LibBaseModules.DB
{
    /// <summary>
    /// 数据基类，重写ToString方法，反射提取数据值
    /// </summary>
    public class BaseNode
    {
        public override string ToString()
        {
            var sb = new StringBuilder();
            var properties = GetType().GetProperties();

            foreach(var prop in properties)
            {
                // 获取Description特性
                var description = prop.GetCustomAttribute<DescriptionAttribute>()?.Description ?? prop.Name;

                // 获取属性值
                var value = prop.GetValue(this);

                // 处理不同数据类型
                switch(value)
                {
                    case byte b:
                        sb.AppendLine($"{description}: {b}");
                        break;
                    case sbyte b:
                        sb.AppendLine($"{description}: {b}");
                        break;
                    case short s:
                        sb.AppendLine($"{description}: {s}");
                        break;
                    case ushort s:
                        sb.AppendLine($"{description}: {s}");
                        break;
                    case int i:
                        sb.AppendLine($"{description}: {i}");
                        break;
                    case uint i:
                        sb.AppendLine($"{description}: {i}");
                        break;
                    case long l:
                        sb.AppendLine($"{description}: {l}");
                        break;
                    case ulong l:
                        sb.AppendLine($"{description}: {l}");
                        break;
                    case float f:
                        sb.AppendLine($"{description}: {f:0.0000}");
                        break;
                    case double d:
                        sb.AppendLine($"{description}: {d:0.0000}");
                        break;
                    case decimal m:
                        sb.AppendLine($"{description}: {m:0.000000}");
                        break;
                    case string str:
                        sb.AppendLine($"{description}: {str}");
                        break;
                    case char c:
                        sb.AppendLine($"{description}: {c}");
                        break;
                    case bool b:
                        sb.AppendLine($"{description}: {b.ToString()}");
                        break;
                    case DateTime date:
                        sb.AppendLine($"{description}: {date:yyyy-MM-dd HH:mm:ss}");
                        break;
                    case Enum e:
                        sb.AppendLine($"{description}: {e.GetDescription()}");
                        break;
                    case IEnumerable enumerable when !(value is string):  // 排除字符串
                        var count = GetEnumerableCount(enumerable);
                        sb.AppendLine($"{description}: 数据量 = {count}");
                        break;
                    default:
                        sb.AppendLine($"{description}").AppendLine($"{value?.ToString() ?? "null"}");
                        break;
                }
            }

            return sb.ToString();
        }

        /// <summary>
        /// 获取IEnumerable的元素数量
        /// </summary>
        /// <param name="enumerable"></param>
        /// <returns></returns>
        private static int GetEnumerableCount(IEnumerable enumerable)
        {
            if(enumerable == null) return 0;

            // 优先尝试获取ICollection的Count属性
            if(enumerable is ICollection collection)
            {
                return collection.Count;
            }

            // 尝试获取数组的Length属性
            if(enumerable is Array array)
            {
                return array.Length;
            }
            // 通用方案：遍历元素计数
            var count = 0;
            var enumerator = enumerable.GetEnumerator();
            while(enumerator.MoveNext())
            {
                count++;
            }
            return count;
        }
    }
}