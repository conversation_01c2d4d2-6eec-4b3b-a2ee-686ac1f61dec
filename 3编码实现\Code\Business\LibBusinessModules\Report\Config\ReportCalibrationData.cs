﻿using SqlSugar;
using System;
using System.ComponentModel;

namespace LibBusinessModules.Report.Config
{
    /// <summary>
    /// 报表计算用校准数据，不区分设备类型
    /// </summary>
    public class ReportCalibrationData
    {
        /// <summary>
        /// 量程
        ///</summary>
        [Description("量程")]
        public double Range { get; set; }

        /// <summary>
        /// 标1测量值
        ///</summary>
        [Description("标1测量值")]
        public double Standard1Value { get; set; }

        /// <summary>
        /// 标1数据时间
        ///</summary>
        [Description("标1数据时间")]
        public DateTime Standard1Time { get; set; }

        /// <summary>
        /// 标2测量值
        ///</summary>
        [Description("标2测量值")]
        public double Standard2Value { get; set; }

        /// <summary>
        /// 标2数据时间
        ///</summary>
        [Description("标2数据时间")]
        public DateTime Standard2Time { get; set; }

        /// <summary>
        /// 标M测量值
        ///</summary>
        [Description("标M测量值")]
        public double StandardMValue { get; set; }

        /// <summary>
        /// 标M数据时间
        ///</summary>
        [Description("标M数据时间")]
        public DateTime StandardMTime { get; set; }
    }

    /// <summary>
    /// 报表计算临时用校准数据，不区分设备类型
    /// </summary>
    public class ReportTmpCalibrationData
    {
        /// <summary>
        /// 量程
        ///</summary>
        [Description("量程")]
        public double Range { get; set; }

        /// <summary>
        /// 测量值
        ///</summary>
        [Description("测量值")]
        public double Value { get; set; }

        /// <summary>
        /// 数据时间
        ///</summary>
        [Description("数据时间")]
        public DateTime Time { get; set; }

        /// <summary>
        /// 类型
        ///</summary>
        [SugarColumn(ColumnName = "type")]
        [Description("类型")]
        public string Type { get; set; }
    }
}
