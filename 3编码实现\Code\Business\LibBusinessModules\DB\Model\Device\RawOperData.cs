﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 操作日志
    ///</summary>
    [SugarTable("operData")]
    [Description("操作日志")]
    public class RawOperData
    {
        /// <summary>
        /// 时间
        ///</summary>
        [SugarColumn(ColumnName = "time")]
        [Description("时间")]
        public string? Time { get; set; }

        /// <summary>
        /// 操作码
        ///</summary>
        [SugarColumn(ColumnName = "code")]
        [Description("操作码")]
        public string? Code { get; set; }

        /// <summary>
        /// 操作人
        ///</summary>
        [SugarColumn(ColumnName = "src")]
        [Description("操作人")]
        public string? Src { get; set; }

        /// <summary>
        /// 详情
        ///</summary>
        [SugarColumn(ColumnName = "msg")]
        [Description("详情")]
        public string? Msg { get; set; }
    }
}