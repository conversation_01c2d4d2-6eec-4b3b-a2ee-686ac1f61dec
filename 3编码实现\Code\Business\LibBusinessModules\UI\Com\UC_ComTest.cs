﻿using Sunny.UI;
using System;
using System.IO.Ports;
using System.Text;
using System.Windows.Forms;

namespace LibBusinessModules.UI.Com
{
    /// <summary>
    /// 通信测试
    /// </summary>
    public partial class UC_ComTest : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 串口通信对象
        /// </summary>
        private SerialPort _serialPort;

        /// <summary>
        /// 当前连接状态
        /// </summary>
        private bool _isConnected = false;

        /// <summary>
        /// 是否支持十六进制显示（默认为true）
        /// </summary>
        private bool _isHexMode = true;

        /// <summary>
        /// 发送数据计数
        /// </summary>
        private int _sendCount = 0;

        /// <summary>
        /// 接收数据计数
        /// </summary>
        private int _receiveCount = 0;

        #endregion

        #region 构造

        public UC_ComTest()
        {
            InitializeComponent();
            this.Load += UC_ComTest_Load;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing"></param>
        public new void Dispose()
        {
            // 关闭串口
            if(_serialPort != null)
            {
                if(_serialPort.IsOpen)
                {
                    _serialPort.Close();
                }
                _serialPort.Dispose();
                _serialPort = null;
            }
            base.Dispose();
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 控件加载事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void UC_ComTest_Load(object sender, EventArgs e)
        {
            try
            {
                // 初始化控件
                InitializeControls();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"控件初始化失败：{ex.Message}");
            }
        }

        #endregion

        #region 初始化

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            // 初始化串口通信对象
            _serialPort = new SerialPort();

            // 绑定串口事件
            _serialPort.DataReceived += SerialPort_DataReceived;
            _serialPort.ErrorReceived += SerialPort_ErrorReceived;

            // 初始化串口选择下拉框
            InitializePortNames();

            // 初始化波特率下拉框
            InitializeBaudRates();

            // 初始化数据位下拉框
            InitializeDataBits();

            // 初始化停止位下拉框
            InitializeStopBits();

            // 初始化奇偶校验下拉框
            InitializeParity();

            // 设置默认值
            SetDefaultValues();

            // 更新控件状态
            UpdateControlsState();

            // 初始化界面状态
            InitializeUIState();
        }

        /// <summary>
        /// 初始化串口名称
        /// </summary>
        private void InitializePortNames()
        {
            cmb_PortName.Items.Clear();
            string[] portNames = SerialPort.GetPortNames();
            foreach(string portName in portNames)
            {
                cmb_PortName.Items.Add(portName);
            }
        }

        /// <summary>
        /// 初始化波特率
        /// </summary>
        private void InitializeBaudRates()
        {
            cmb_BaudRate.Items.Clear();
            cmb_BaudRate.Items.AddRange(new object[] { "1200", "2400", "4800", "9600", "19200", "38400", "57600", "115200" });
        }

        /// <summary>
        /// 初始化数据位
        /// </summary>
        private void InitializeDataBits()
        {
            cmb_DataBits.Items.Clear();
            cmb_DataBits.Items.AddRange(new object[] { "5", "6", "7", "8" });
        }

        /// <summary>
        /// 初始化停止位
        /// </summary>
        private void InitializeStopBits()
        {
            cmb_StopBits.Items.Clear();
            cmb_StopBits.Items.AddRange(new object[] { "1", "1.5", "2" });
        }

        /// <summary>
        /// 初始化奇偶校验
        /// </summary>
        private void InitializeParity()
        {
            cmb_Parity.Items.Clear();
            cmb_Parity.Items.AddRange(new object[] { "无", "奇校验", "偶校验" });
        }

        /// <summary>
        /// 设置默认值
        /// </summary>
        private void SetDefaultValues()
        {
            if(cmb_PortName.Items.Count > 0)
                cmb_PortName.SelectedIndex = 0;

            cmb_BaudRate.Text = "9600";
            cmb_DataBits.Text = "8";
            cmb_StopBits.Text = "1";
            cmb_Parity.Text = "无";
        }

        /// <summary>
        /// 初始化界面状态
        /// </summary>
        private void InitializeUIState()
        {
            // 设置接收区域为只读
            txt_ReceiveData.ReadOnly = true;

            // 初始化数据计数
            _sendCount = 0;
            _receiveCount = 0;

            // 设置初始状态
            _isConnected = false;
            _isHexMode = true; // 默认使用十六进制模式
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 连接/断开按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn_Connect_Click(object sender, EventArgs e)
        {
            try
            {
                if(!_isConnected)
                {
                    // 连接串口
                    ConnectSerial();
                }
                else
                {
                    // 断开串口
                    DisconnectSerial();
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"操作失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 发送按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn_Send_Click(object sender, EventArgs e)
        {
            try
            {
                if(!_isConnected)
                {
                    UIMessageBox.ShowWarning("请先连接串口！");
                    return;
                }

                if(string.IsNullOrEmpty(txt_SendData.Text))
                {
                    UIMessageBox.ShowWarning("请输入要发送的数据！");
                    return;
                }

                SendData();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"发送数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 清空发送区按钮点击事件（如果存在）
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn_ClearSend_Click(object sender, EventArgs e)
        {
            try
            {
                txt_SendData.Text = "";
                txt_SendData.Focus();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"清空发送区失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 清空接收区按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn_ClearReceive_Click(object sender, EventArgs e)
        {
            try
            {
                txt_ReceiveData.Text = "";
                _sendCount = 0;
                _receiveCount = 0;
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"清空接收区失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 串口参数改变事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void SerialParam_Changed(object sender, EventArgs e)
        {
            try
            {
                if(_isConnected)
                {
                    AppendReceiveData("[警告] 串口已连接，参数更改将在下次连接时生效");
                }
            }
            catch(Exception ex)
            {
                AppendReceiveData($"[错误] 参数更改处理失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 发送数据文本框回车事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void txt_SendData_KeyPress(object sender, KeyPressEventArgs e)
        {
            try
            {
                // 按回车键发送数据
                if(e.KeyChar == (char)Keys.Enter)
                {
                    e.Handled = true; // 阻止默认的回车处理
                    if(_isConnected && !string.IsNullOrEmpty(txt_SendData.Text))
                    {
                        SendData();
                    }
                    return;
                }

                // 在十六进制模式下，只允许输入十六进制字符和空格
                if(_isHexMode)
                {
                    // 允许的字符：0-9, A-F, a-f, 空格, 退格键
                    if(!IsHexChar(e.KeyChar) && e.KeyChar != ' ' && e.KeyChar != (char)Keys.Back)
                    {
                        e.Handled = true; // 阻止输入非法字符
                        return;
                    }
                }
            }
            catch(Exception ex)
            {
                AppendReceiveData($"[{DateTime.Now:HH:mm:ss}][错误]: 按键处理失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 串口数据接收事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                SerialPort sp = (SerialPort)sender;
                int bytesToRead = sp.BytesToRead;
                if(bytesToRead > 0)
                {
                    byte[] buffer = new byte[bytesToRead];
                    sp.Read(buffer, 0, bytesToRead);

                    // 更新接收计数
                    _receiveCount++;

                    // 显示接收的数据（始终以十六进制显示）
                    string receiveDisplay = BytesToHex(buffer);
                    AppendReceiveData($"[{DateTime.Now:HH:mm:ss}][接收]({buffer.Length}字节): {receiveDisplay}");
                }
            }
            catch(Exception ex)
            {
                AppendReceiveData($"[{DateTime.Now:HH:mm:ss}][错误]: 接收数据失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 串口错误事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void SerialPort_ErrorReceived(object sender, SerialErrorReceivedEventArgs e)
        {
            AppendReceiveData($"[{DateTime.Now:HH:mm:ss}][错误]: 串口错误 - {e.EventType}");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 连接串口
        /// </summary>
        private void ConnectSerial()
        {
            // 验证参数
            if(!ValidateSerialParams())
            {
                return;
            }

            // 获取串口参数
            string portName = cmb_PortName.Text;
            int baudRate = int.Parse(cmb_BaudRate.Text);
            int dataBits = int.Parse(cmb_DataBits.Text);

            // 转换停止位
            StopBits stopBits = StopBits.One;
            switch(cmb_StopBits.Text)
            {
                case "1":
                    stopBits = StopBits.One;
                    break;
                case "1.5":
                    stopBits = StopBits.OnePointFive;
                    break;
                case "2":
                    stopBits = StopBits.Two;
                    break;
            }

            // 转换奇偶校验
            Parity parity = Parity.None;
            switch(cmb_Parity.Text)
            {
                case "无":
                    parity = Parity.None;
                    break;
                case "奇校验":
                    parity = Parity.Odd;
                    break;
                case "偶校验":
                    parity = Parity.Even;
                    break;
                case "标志校验":
                    parity = Parity.Mark;
                    break;
                case "空格校验":
                    parity = Parity.Space;
                    break;
                default:
                    parity = Parity.None;
                    break;
            }

            // 配置串口参数
            _serialPort.PortName = portName;
            _serialPort.BaudRate = baudRate;
            _serialPort.DataBits = dataBits;
            _serialPort.StopBits = stopBits;
            _serialPort.Parity = parity;
            _serialPort.ReadTimeout = 2000;
            _serialPort.WriteTimeout = 2000;

            // 打开串口
            _serialPort.Open();

            _isConnected = true;
            UpdateControlsState();

            AppendReceiveData($"[{DateTime.Now:HH:mm:ss}][系统] : 串口已打开");
        }

        /// <summary>
        /// 断开串口
        /// </summary>
        private void DisconnectSerial()
        {
            if(_serialPort != null && _serialPort.IsOpen)
            {
                _serialPort.Close();
            }
            _isConnected = false;
            UpdateControlsState();

            AppendReceiveData($"[{DateTime.Now:HH:mm:ss}][系统] : 串口已断开");
        }

        /// <summary>
        /// 发送数据
        /// </summary>
        private void SendData()
        {
            string sendText = txt_SendData.Text.Trim();
            if(string.IsNullOrEmpty(sendText))
            {
                UIMessageBox.ShowWarning("发送数据不能为空！");
                return;
            }

            try
            {
                // 根据十六进制模式处理发送数据
                byte[] sendBytes;
                if(_isHexMode)
                {
                    // 十六进制模式：将十六进制字符串转换为字节数组
                    sendBytes = HexToBytes(sendText);
                    if(sendBytes == null || sendBytes.Length == 0)
                    {
                        UIMessageBox.ShowWarning("十六进制数据格式错误！请输入有效的十六进制数据，如：01 02 03 FF");
                        return;
                    }
                }
                else
                {
                    // 文本模式：将文本转换为字节数组
                    sendBytes = Encoding.UTF8.GetBytes(sendText);
                }

                // 发送数据
                _serialPort.Write(sendBytes, 0, sendBytes.Length);

                // 更新发送计数
                _sendCount++;

                // 显示发送的数据（始终以十六进制显示）
                string sendDisplay = BytesToHex(sendBytes);
                AppendReceiveData($"[{DateTime.Now:HH:mm:ss}][发送] ({sendBytes.Length}字节): {sendDisplay}");
            }
            catch(Exception ex)
            {
                AppendReceiveData($"[{DateTime.Now:HH:mm:ss}][错误] : 发送失败 - {ex.Message}");
                throw; // 重新抛出异常，让上层处理
            }
        }

        /// <summary>
        /// 检查字符是否为十六进制字符
        /// </summary>
        /// <param name="c"></param>
        /// <returns></returns>
        private bool IsHexChar(char c)
        {
            return (c >= '0' && c <= '9') || (c >= 'A' && c <= 'F') || (c >= 'a' && c <= 'f');
        }

        /// <summary>
        /// 字节数组转十六进制字符串
        /// </summary>
        /// <param name="bytes"></param>
        /// <returns></returns>
        private string BytesToHex(byte[] bytes)
        {
            if(bytes == null || bytes.Length == 0)
                return "";

            StringBuilder sb = new StringBuilder();
            for(int i = 0; i < bytes.Length; i++)
            {
                sb.Append(bytes[i].ToString("X2"));
                if(i < bytes.Length - 1)
                    sb.Append(" ");
            }
            return sb.ToString();
        }

        /// <summary>
        /// 十六进制字符串转字节数组
        /// </summary>
        /// <param name="hex"></param>
        /// <returns></returns>
        private byte[] HexToBytes(string hex)
        {
            if(string.IsNullOrEmpty(hex))
                return new byte[0];

            hex = hex.Replace(" ", "").Replace("-", "");
            if(hex.Length % 2 != 0)
                throw new ArgumentException("十六进制字符串长度必须为偶数");

            byte[] bytes = new byte[hex.Length / 2];
            for(int i = 0; i < bytes.Length; i++)
            {
                bytes[i] = Convert.ToByte(hex.Substring(i * 2, 2), 16);
            }
            return bytes;
        }

        /// <summary>
        /// 追加接收数据显示
        /// </summary>
        /// <param name="data"></param>
        private void AppendReceiveData(string data)
        {
            if(this.InvokeRequired)
            {
                this.Invoke(new Action<string>(AppendReceiveData), data);
                return;
            }

            txt_ReceiveData.AppendText(data + Environment.NewLine);
            txt_ReceiveData.ScrollToCaret();
        }

        /// <summary>
        /// 更新控件状态
        /// </summary>
        private void UpdateControlsState()
        {
            try
            {
                // 连接状态下禁用串口参数配置
                cmb_PortName.Enabled = !_isConnected;
                cmb_BaudRate.Enabled = !_isConnected;
                cmb_DataBits.Enabled = !_isConnected;
                cmb_StopBits.Enabled = !_isConnected;
                cmb_Parity.Enabled = !_isConnected;

                // 更新连接状态指示灯
                uiLight.State = _isConnected ? UILightState.On : UILightState.Off;

                // 更新连接按钮文本和颜色
                btn_Connect.Text = _isConnected ? "断开" : "连接";
                btn_Connect.FillColor = _isConnected ? System.Drawing.Color.FromArgb(220, 155, 155) : System.Drawing.Color.FromArgb(80, 160, 255);

                // 发送相关控件状态
                btn_Send.Enabled = _isConnected;

                // 更新状态显示
                UpdateStatusDisplay();
            }
            catch(Exception ex)
            {
                AppendReceiveData($"[{DateTime.Now:HH:mm:ss}][错误] 更新控件状态失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新状态显示
        /// </summary>
        private void UpdateStatusDisplay()
        {
            try
            {
                string statusText = _isConnected ?
                    $"已连接 - {cmb_PortName.Text}@{cmb_BaudRate.Text}" :
                    "未连接";

                // 在接收区域显示状态信息
                if(_isConnected)
                {
                    // 可以在这里添加状态栏或其他状态显示逻辑
                    // 暂时通过窗口标题显示状态
                    this.Text = $"通信测试 - {statusText} - 发送:{_sendCount} 接收:{_receiveCount}";
                }
                else
                {
                    this.Text = "通信测试 - 未连接";
                }
            }
            catch(Exception ex)
            {
                AppendReceiveData($"[{DateTime.Now:HH:mm:ss}][错误] 更新状态显示失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 验证串口参数
        /// </summary>
        /// <returns></returns>
        private bool ValidateSerialParams()
        {
            try
            {
                if(string.IsNullOrEmpty(cmb_PortName.Text))
                {
                    UIMessageBox.ShowWarning("请选择串口！");
                    cmb_PortName.Focus();
                    return false;
                }

                if(string.IsNullOrEmpty(cmb_BaudRate.Text))
                {
                    UIMessageBox.ShowWarning("请选择波特率！");
                    cmb_BaudRate.Focus();
                    return false;
                }

                if(!int.TryParse(cmb_BaudRate.Text, out int baudRate) || baudRate <= 0)
                {
                    UIMessageBox.ShowWarning("波特率格式不正确！");
                    cmb_BaudRate.Focus();
                    return false;
                }

                if(string.IsNullOrEmpty(cmb_DataBits.Text))
                {
                    UIMessageBox.ShowWarning("请选择数据位！");
                    cmb_DataBits.Focus();
                    return false;
                }

                if(string.IsNullOrEmpty(cmb_StopBits.Text))
                {
                    UIMessageBox.ShowWarning("请选择停止位！");
                    cmb_StopBits.Focus();
                    return false;
                }

                if(string.IsNullOrEmpty(cmb_Parity.Text))
                {
                    UIMessageBox.ShowWarning("请选择奇偶校验！");
                    cmb_Parity.Focus();
                    return false;
                }

                return true;
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"参数验证失败：{ex.Message}");
                return false;
            }
        }

        #endregion
    }
}