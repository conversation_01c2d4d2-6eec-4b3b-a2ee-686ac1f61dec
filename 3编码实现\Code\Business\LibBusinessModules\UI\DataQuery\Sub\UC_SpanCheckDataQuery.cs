﻿using LibBaseModules.Helper;
using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using Sunny.UI;
using System.Windows.Forms;

namespace LibBusinessModules.UI.DataQuery
{
    /// <summary>
    /// 跨度核查数据查询
    /// </summary>
    public partial class UC_SpanCheckDataQuery : UC_DataQueryBase
    {
        public UC_SpanCheckDataQuery()
        {
            InitializeComponent();
            QueryDataName = "跨度核查数据";
        }

        protected override void SetViewHead()
        {
            dgvRecords.Columns.Clear();
            dgvRecords.Columns.Add("Num", "序号");
            dgvRecords.Columns.Add("SNCode", "设备序列号");
            dgvRecords.Columns.Add("Time", "数据时间");
            dgvRecords.Columns.Add("Value", "测量值");
            dgvRecords.Columns.Add("Standard", "标准值");
            dgvRecords.Columns.Add("Err", "误差");
            dgvRecords.Columns.Add("Judge", "误差判定");
            dgvRecords.Columns.Add("ErrDif", "漂移");
            dgvRecords.Columns.Add("JudgeDif", "漂移判定");
            dgvRecords.Columns.Add("Time1", "上次跨度样时间");
            dgvRecords.Columns.Add("Time2", "本次跨度样时间");
        }

        protected override int QueryDataCount()
        {
            var query = DBHelper.GetPCDBContext().Queryable<SpanCheckData>()
                           .Where(data => data.Time >= StartTime && data.Time <= EndTime);

            if(!string.IsNullOrEmpty(SnCode))
            {
                query = query.Where(data => data.SNCode.Contains(SnCode));
            }

            return query.Count();
        }

        protected override void FillDataToDgv()
        {
            try
            {
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据查询中，请稍候...");

                // 查询当前页数据
                var query = DBHelper.GetPCDBContext().Queryable<SpanCheckData>()
                               .Where(data => data.Time >= StartTime && data.Time <= EndTime);
                if(!string.IsNullOrEmpty(SnCode))
                {
                    query = query.Where(data => data.SNCode.Contains(SnCode));
                }
                var dataList = query.ToPageList(CurPage - 1, PageSize);

                UIFormServiceHelper.HideWaitForm(this.ParentForm);

                UIFormServiceHelper.ShowStatusForm(this.ParentForm, dataList.Count, "数据渲染中，请稍候...");

                int index = 1;
                foreach(var data in dataList)
                {
                    int rowIndex = dgvRecords.AddRow();
                    DataGridViewRow dr = dgvRecords.Rows[rowIndex];
                    dr.Cells["Num"].Value = index;
                    dr.Cells["SNCode"].Value = data.SNCode;
                    dr.Cells["Time"].Value = data.Time.ToDisplayFormat();
                    dr.Cells["Value"].Value = data.Value.ToString("F4");
                    dr.Cells["Standard"].Value = data.Standard.ToString("F4");
                    dr.Cells["Err"].Value = data.Err.ToString("F4");
                    dr.Cells["Judge"].Value = data.Judge;
                    dr.Cells["ErrDif"].Value = data.ErrDif.ToString("F4");
                    dr.Cells["JudgeDif"].Value = data.JudgeDif;
                    dr.Cells["Time1"].Value = data.Time1.ToDisplayFormat();
                    dr.Cells["Time2"].Value = data.Time2.ToDisplayFormat();

                    dr.Tag = data;

                    UIFormServiceHelper.SetStatusFormDescription(this.ParentForm, $"数据渲染中[{index++}/{dataList.Count}]......");
                    UIFormServiceHelper.SetStatusFormStepIt(this.ParentForm, index);
                    // 线程切换，防止最终进度界面无法关闭
                    //Thread.Sleep(1);
                }
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                //Thread.Sleep(100);
                // 隐藏进度条界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
                UIFormServiceHelper.HideStatusForm(this.ParentForm);
            }
        }
    }
}