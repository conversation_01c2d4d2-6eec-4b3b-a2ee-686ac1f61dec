﻿using LibBaseModules.DB;
using LibBusinessModules.DB.Models.Device;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.PC
{
    /// <summary>
    /// 操作日志
    ///</summary>
    [SugarTable("oper_data")]
    [SugarIndex("index_sncode_time", nameof(SNCode), OrderByType.Asc, nameof(Time), OrderByType.Asc)]
    [Description("操作日志")]
    public class OperData : BaseNode
    {
        #region 字段属性

        /// <summary>
        /// 设备序列号
        ///</summary>
        [SugarColumn(ColumnName = "sncode", ColumnDescription = "设备序列号")]
        [Description("设备序列号")]
        public string SNCode { get; set; }

        /// <summary>
        /// 数据时间
        ///</summary>
        [SugarColumn(ColumnName = "time", SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "数据时间")]
        [Description("数据时间")]
        public DateTime Time { get; set; }

        /// <summary>
        /// 操作码
        ///</summary>
        [SugarColumn(ColumnName = "code", ColumnDescription = "操作码")]
        [Description("操作码")]
        public string Code { get; set; }

        /// <summary>
        /// 操作人
        ///</summary>
        [SugarColumn(ColumnName = "src", ColumnDescription = "操作人")]
        [Description("操作人")]
        public string Src { get; set; }

        /// <summary>
        /// 详情
        ///</summary>
        [SugarColumn(ColumnName = "msg", ColumnDescription = "详情")]
        [Description("详情")]
        public string Msg { get; set; }

        #endregion

        #region 公共方法

        /// <summary>
        /// 从设备数据库生成数据
        /// </summary>
        public static List<OperData> InitFromDeviceDB(SqlSugarClient db, string snCode)
        {
            try
            {
                List<OperData> dataList = new List<OperData>();

                List<RawOperData> operDataList = db.Queryable<RawOperData>().ToList();
                foreach(RawOperData rawOperData in operDataList)
                {
                    try
                    {
                        OperData operData = new OperData();
                        operData.SNCode = snCode;
                        operData.Time = DateTime.Parse(rawOperData.Time);
                        operData.Code = rawOperData.Code;
                        operData.Src = rawOperData.Src;
                        operData.Msg = rawOperData.Msg;

                        dataList.Add(operData);
                    }
                    catch
                    {
                    }
                }

                return dataList;
            }
            catch(Exception ex)
            {
                throw new Exception($"从数据库提取操作日志出错：{ex.Message}");
            }
        }

        #endregion
    }
}