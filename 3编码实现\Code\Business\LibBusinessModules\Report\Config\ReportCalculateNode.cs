﻿using LibBusinessModules.Config;
using LibBusinessModules.DB.Models.PC;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace LibBusinessModules.Report.Config
{
    /// <summary>
    /// 报表计算用数据
    /// 存储单个设备报表导出相关所有信息。数据去除无效信息，不包含各设备不同类型
    /// </summary>
    public class ReportCalculateNode
    {
        #region 字段属性

        #region 设备数据

        /// <summary>
        /// 设备序列号
        ///</summary>
        [Description("设备序列号")]
        public string SNCode { get; }

        /// <summary>
        /// 设备基础信息
        /// </summary>
        [Description("设备基础信息")]
        public DeviceInfo DevInfo { get; set; }

        /// <summary>
        /// 光源信息
        /// </summary>
        [Description("光源信息")]
        public LightSourceInfo LightInfo { get; set; }

        /// <summary>
        /// 设备配置
        /// </summary>
        [Description("设备配置")]
        public DeviceConfig DeviceConfigInfo { get; set; }

        /// <summary>
        /// 标准量程测量数据
        /// </summary>
        [Description("标准量程测量数据")]
        public List<ReportMeasureData> StandardRangeMeasureData { get; set; }

        /// <summary>
        /// 辅助量程测量数据
        /// </summary>
        [Description("辅助量程测量数据")]
        public List<ReportMeasureData> AuxiliaryRangeMeasureData { get; set; }

        /// <summary>
        /// 标准量程曲线数据
        /// </summary>
        [Description("标准量程曲线数据")]
        public ReportCurveData StandardRangeCurveData { get; set; }

        /// <summary>
        /// 辅助量程曲线数据
        /// </summary>
        [Description("辅助量程曲线数据")]
        public ReportCurveData AuxiliaryRangeCurveData { get; set; }

        /// <summary>
        /// 标准量程校准数据
        /// </summary>
        [Description("标准量程校准数据")]
        public ReportCalibrationData StandardRangeCalibrationData { get; set; }

        /// <summary>
        /// 辅助量程校准数据
        /// </summary>
        [Description("辅助量程校准数据")]
        public ReportCalibrationData AuxiliaryRangeCalibrationData { get; set; }

        #endregion

        #region 辅助数据

        /// <summary>
        /// 环境温度
        ///</summary>
        [Description("环境温度")]
        public double Temperature { get; set; }

        /// <summary>
        /// 环境湿度
        ///</summary>
        [Description("环境湿度")]
        public double Humidity { get; set; }

        /// <summary>
        /// 检验员姓名
        ///</summary>
        [Description("检验员姓名")]
        public string InspectorName { get; set; }

        /// <summary>
        /// 审核员姓名
        ///</summary>
        [Description("审核员姓名")]
        public string AuditorName { get; set; }

        #endregion

        #endregion

        #region 构造

        public ReportCalculateNode(string snCode)
        {
            SNCode = snCode;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 计算数据，生成数据报告所用原始数据
        /// </summary>
        public void CalculatedData()
        {
            // 主量程计算
            ProcessRangeData(StandardRangeMeasureData);

            // 辅助量程计算
            ProcessRangeData(AuxiliaryRangeMeasureData);
        }

        /// <summary>
        /// 处理量程数据的通用逻辑
        /// </summary>
        /// <param name="measureDataList">测量数据列表</param>
        private void ProcessRangeData(List<ReportMeasureData> measureDataList)
        {
            ReportMeasureData zeroPointDriftData = null;
            ReportMeasureData measurementRangeDriftData = null;

            foreach(ReportMeasureData data in measureDataList)
            {
                data.CalculatedData(DevInfo);
                if(data.MeasureItem.ItemType == eMeasureItemType.ZeroPointDrift)
                {
                    zeroPointDriftData = data;
                }
                else if(data.MeasureItem.ItemType == eMeasureItemType.MeasurementRangeDrift)
                {
                    measurementRangeDriftData = data;
                }
            }

            if(zeroPointDriftData == null && measurementRangeDriftData != null)
            {
                throw new Exception("启用量程漂移时必须启用零点漂移！");
            }

            // 量程漂移数据结果用到零点漂移。此处减去
            if(zeroPointDriftData != null && measurementRangeDriftData != null)
            {
                measurementRangeDriftData.ResultValue -= zeroPointDriftData.ResultValue;
            }
        }

        #region 模拟数据

        /// <summary>
        /// 生成模拟测量数据
        /// </summary>
        /// <param name="calculateNode">报表计算节点</param>
        public static void GenerateMockMeasureData(ReportCalculateNode calculateNode)
        {
            // 生成标准量程测量数据
            calculateNode.StandardRangeMeasureData = GenerateMockRangeData(calculateNode, true);

            // 生成辅助量程测量数据
            calculateNode.AuxiliaryRangeMeasureData = GenerateMockRangeData(calculateNode, false);
        }

        /// <summary>
        /// 生成模拟量程数据
        /// </summary>
        private static List<ReportMeasureData> GenerateMockRangeData(ReportCalculateNode calculateNode, bool isStandardRange)
        {
            var mockData = new List<ReportMeasureData>();
            var random = new Random();

            // 获取量程配置
            var rangeConfig = isStandardRange ?
                calculateNode.DeviceConfigInfo.StandardRangeMeasureItems :
                calculateNode.DeviceConfigInfo.AuxiliaryRangeMeasureItems;

            // 先处理零点漂移数据
            var zeroPointDriftItem = rangeConfig.FirstOrDefault(item =>
                item.IsUsed && item.ItemType == eMeasureItemType.ZeroPointDrift);

            // 再处理量程漂移数据
            var measurementRangeDriftItem = rangeConfig.FirstOrDefault(item =>
                item.IsUsed && item.ItemType == eMeasureItemType.MeasurementRangeDrift);

            // 如果启用了量程漂移但未启用零点漂移，抛出异常
            if(measurementRangeDriftItem != null && zeroPointDriftItem == null)
            {
                throw new System.Exception("启用量程漂移时必须启用零点漂移！");
            }

            // 生成每个测量点的数据
            foreach(var measureItem in rangeConfig)
            {
                if(!measureItem.IsUsed) continue;

                var measureData = new ReportMeasureData
                {
                    MeasureItem = measureItem,
                    Range = isStandardRange ? calculateNode.DeviceConfigInfo.StandardRange : calculateNode.DeviceConfigInfo.AuxiliaryRange,
                    MeasureDataList = new List<ReportTmpMeasureData>()
                };

                // 根据 CalculableDataCount 生成对应数量的测量数据点
                for(int j = 0; j < measureItem.CalculableDataCount; j++)
                {
                    var standValue = measureItem.StandValue;
                    double measureValue;

                    if(measureItem.ItemType == eMeasureItemType.ZeroPointDrift)
                    {
                        // 零点漂移：在标准值±10%范围内生成较小的随机值
                        var minValue = standValue * 0.9;
                        var maxValue = standValue * 1.1;
                        var range = maxValue - minValue;
                        measureValue = minValue + (random.NextDouble() * range * 0.1); // 限制在更小的范围内
                    }
                    else if(measureItem.ItemType == eMeasureItemType.MeasurementRangeDrift)
                    {
                        // 量程漂移：在标准值±10%范围内生成较大的随机值
                        var minValue = standValue * 0.9;
                        var maxValue = standValue * 1.1;
                        var range = maxValue - minValue;
                        measureValue = minValue + (random.NextDouble() * range * 0.8 + range * 0.1); // 限制在较大的范围内
                    }
                    else
                    {
                        // 其他测量项：在标准值±10%范围内随机
                        var minValue = standValue * 0.9;
                        var maxValue = standValue * 1.1;
                        var range = maxValue - minValue;
                        measureValue = minValue + (random.NextDouble() * range);
                    }

                    measureData.MeasureDataList.Add(new ReportTmpMeasureData
                    {
                        Time = DateTime.Now.AddMinutes(-j),
                        StandValue = standValue,
                        MeasureValue = measureValue,
                        Range = measureData.Range
                    });
                }

                mockData.Add(measureData);
            }

            return mockData;
        }

        #endregion

        #endregion
    }
}