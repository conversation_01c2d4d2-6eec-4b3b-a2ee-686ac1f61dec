{"Version": 1, "WorkspaceRootPath": "G:\\01-My<PERSON><PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\IAutoTestX\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{33812915-0DF5-4F6C-A55B-9F1185734B4F}|IAutoTestX\\IAutoTestX.csproj|g:\\01-mycode\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\code\\iautotestx\\iautotestx\\form1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{33812915-0DF5-4F6C-A55B-9F1185734B4F}|IAutoTestX\\IAutoTestX.csproj|solutionrelative:iautotestx\\form1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 9, "Children": [{"$type": "Bookmark", "Name": "ST:131:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:134:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:135:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:137:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:136:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Form1.cs [设计]", "DocumentMoniker": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\IAutoTestX\\IAutoTestX\\Form1.cs", "RelativeDocumentMoniker": "IAutoTestX\\Form1.cs", "ToolTip": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\IAutoTestX\\IAutoTestX\\Form1.cs [设计]", "RelativeToolTip": "IAutoTestX\\Form1.cs [设计]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-22T08:09:04.213Z", "EditorCaption": " [设计]"}]}]}]}