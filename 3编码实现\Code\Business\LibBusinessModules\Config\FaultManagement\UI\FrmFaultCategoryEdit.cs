using LibBusinessModules.Config.FaultManagement.Models;
using Sunny.UI;
using System;
using System.Windows.Forms;

namespace LibBusinessModules.Config.FaultManagement.UI
{
    /// <summary>
    /// 故障分类编辑窗体
    /// </summary>
    public partial class FrmFaultCategoryEdit : UIForm
    {
        #region 字段

        /// <summary>
        /// 故障分类级别
        /// </summary>
        private readonly FaultCategoryLevel _level;

        /// <summary>
        /// 当前编辑的分类对象
        /// </summary>
        private object _category;

        /// <summary>
        /// 父分类ID（仅二级分类使用）
        /// </summary>
        private readonly int _parentId;

        /// <summary>
        /// 是否为编辑模式
        /// </summary>
        private readonly bool _isEditMode;

        #endregion

        #region 构造

        /// <summary>
        /// 新增一级分类构造函数
        /// </summary>
        /// <param name="level">分类级别</param>
        public FrmFaultCategoryEdit(FaultCategoryLevel level)
        {
            InitializeComponent();
            _level = level;
            _isEditMode = false;
            
            if (_level == FaultCategoryLevel.Level1)
            {
                this.Text = "新增一级故障分类";
            }
            else
            {
                this.Text = "新增二级故障分类";
            }
        }

        /// <summary>
        /// 编辑一级分类构造函数
        /// </summary>
        /// <param name="level">分类级别</param>
        /// <param name="category">分类对象</param>
        public FrmFaultCategoryEdit(FaultCategoryLevel level, object category)
        {
            InitializeComponent();
            _level = level;
            _category = category;
            _isEditMode = true;
            
            if (_level == FaultCategoryLevel.Level1)
            {
                this.Text = "编辑一级故障分类";
            }
            else
            {
                this.Text = "编辑二级故障分类";
            }
        }

        /// <summary>
        /// 新增二级分类构造函数
        /// </summary>
        /// <param name="level">分类级别</param>
        /// <param name="category">分类对象，为null表示新增</param>
        /// <param name="parentId">父分类ID</param>
        public FrmFaultCategoryEdit(FaultCategoryLevel level, object category, int parentId)
        {
            InitializeComponent();
            _level = level;
            _category = category;
            _parentId = parentId;
            _isEditMode = category != null;
            
            if (_isEditMode)
            {
                this.Text = "编辑二级故障分类";
            }
            else
            {
                this.Text = "新增二级故障分类";
            }
        }

        #endregion

        #region 事件

        private void FrmFaultCategoryEdit_Load(object sender, EventArgs e)
        {
            // 如果是编辑模式，加载现有数据
            if (_isEditMode && _category != null)
            {
                if (_level == FaultCategoryLevel.Level1 && _category is FaultCategory1 category1)
                {
                    txtCategoryName.Text = category1.CategoryName;
                    txtDescription.Text = category1.Description;
                }
                else if (_level == FaultCategoryLevel.Level2 && _category is FaultCategory2 category2)
                {
                    txtCategoryName.Text = category2.CategoryName;
                    txtDescription.Text = category2.Description;
                }
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证输入
                if (string.IsNullOrWhiteSpace(txtCategoryName.Text))
                {
                    UIMessageBox.ShowError("分类名称不能为空");
                    return;
                }

                // 根据不同级别和模式执行不同操作
                if (_level == FaultCategoryLevel.Level1)
                {
                    if (_isEditMode && _category is FaultCategory1 category1)
                    {
                        // 编辑一级分类
                        category1.CategoryName = txtCategoryName.Text;
                        category1.Description = txtDescription.Text;
                        FaultManager.GetInstance().UpdateCategory1(category1);
                    }
                    else
                    {
                        // 新增一级分类
                        FaultCategory1 newCategory = new FaultCategory1
                        {
                            CategoryName = txtCategoryName.Text,
                            Description = txtDescription.Text
                        };
                        FaultManager.GetInstance().AddCategory1(newCategory);
                    }
                }
                else if (_level == FaultCategoryLevel.Level2)
                {
                    if (_isEditMode && _category is FaultCategory2 category2)
                    {
                        // 编辑二级分类
                        category2.CategoryName = txtCategoryName.Text;
                        category2.Description = txtDescription.Text;
                        FaultManager.GetInstance().UpdateCategory2(category2);
                    }
                    else
                    {
                        // 新增二级分类
                        FaultCategory2 newCategory = new FaultCategory2
                        {
                            Category1Id = _parentId,
                            CategoryName = txtCategoryName.Text,
                            Description = txtDescription.Text
                        };
                        FaultManager.GetInstance().AddCategory2(newCategory);
                    }
                }

                DialogResult = DialogResult.OK;
            }
            catch (Exception ex)
            {
                UIMessageBox.ShowError($"保存失败：{ex.Message}");
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        #endregion
    }
}