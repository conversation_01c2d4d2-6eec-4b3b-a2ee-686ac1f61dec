using LibBaseModules.Helper;
using SqlSugar;
using System;

namespace LibBaseModules.DB
{
    /// <summary>
    /// SqlSugar访问帮助类
    /// </summary>
    public static class SqlSugarHelper
    {
        /// <summary>
        /// 数据库访问器
        /// </summary>
        public static SqlSugarClient GetDBContext(DbType dbType, string connectString)
        {
            if(string.IsNullOrEmpty(connectString))
            {
                throw new Exception("数据库连接串未设置！");
            }

            var connectionConfig = new ConnectionConfig()
            {
                DbType = dbType,
                ConnectionString = connectString,
                InitKeyType = InitKeyType.Attribute,
                IsAutoCloseConnection = true, //自动关闭连接
                AopEvents = new AopEvents
                {
                    OnError = (exp) =>
                    {
                        // 记录错误但不抛出异常
                        LogUtil.GetInstance().LogWrite($"SqlSugar错误: {exp.Message}");
                        // 可以记录到日志文件
                    },
                    OnLogExecuting = (sql, pars) =>
                    {
                        // 记录执行的SQL语句
                        LogUtil.GetInstance().LogWrite(sql);
                    }
                }
            };

            if(dbType == DbType.Sqlite)
            {
                connectionConfig.MoreSettings = new ConnMoreSettings()
                {
                    SqliteCodeFirstEnableDefaultValue = true, //启用默认值
                    SqliteCodeFirstEnableDescription = true, //启用备注
                    IsAutoRemoveDataCache = true //自动删除缓存
                };
            }

            return new SqlSugarClient(connectionConfig);
        }
    }
}


