﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 选向阀序列号
    ///</summary>
    [SugarTable("valve_sn_info")]
    [Description("选向阀序列号")]
    public class ValveSNInfo
    {
        /// <summary>
        /// 序列号
        ///</summary>
        [SugarColumn(ColumnName = "sn")]
        [Description("序列号")]
        public string? SN { get; set; }
    }
}