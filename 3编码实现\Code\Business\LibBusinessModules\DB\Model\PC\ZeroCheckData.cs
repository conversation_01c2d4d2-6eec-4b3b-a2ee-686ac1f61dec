﻿using LibBusinessModules.DB.Models.Device;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.PC
{
    /// <summary>
    /// 零点核查数据
    ///</summary>
    [SugarTable("zerocheck_data")]
    [Description("零点核查数据")]
    public class ZeroCheckData
    {
        #region 字段属性

        /// <summary>
        /// 设备序列号
        ///</summary>
        [SugarColumn(ColumnName = "sncode", IsPrimaryKey = true)]
        [Description("设备序列号")]
        public string SNCode { get; set; }

        /// <summary>
        /// 数据时间
        ///</summary>
        [SugarColumn(ColumnName = "time", IsPrimaryKey = true, SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "数据时间")]
        [Description("数据时间")]
        public DateTime Time { get; set; }

        /// <summary>
        /// 测量值
        ///</summary>
        [SugarColumn(ColumnName = "value", ColumnDescription = "浓度")]
        [Description("测量值")]
        public double Value { get; set; }

        /// <summary>
        /// 标准值
        ///</summary>
        [SugarColumn(ColumnName = "standard", ColumnDescription = "标准值")]
        [Description("标准值")]
        public double Standard { get; set; }

        /// <summary>
        /// 误差
        ///</summary>
        [SugarColumn(ColumnName = "err", ColumnDescription = "误差")]
        [Description("误差")]
        public double Err { get; set; }

        /// <summary>
        /// 误差判定
        ///</summary>
        [SugarColumn(ColumnName = "judge", ColumnDescription = "误差判定")]
        [Description("误差判定")]
        public string Judge { get; set; }

        /// <summary>
        /// 漂移
        ///</summary>
        [SugarColumn(ColumnName = "errDif", ColumnDescription = "漂移")]
        [Description("漂移")]
        public double ErrDif { get; set; }

        /// <summary>
        /// 漂移判定
        ///</summary>
        [SugarColumn(ColumnName = "judgeDif", ColumnDescription = "漂移判定")]
        [Description("漂移判定")]
        public string JudgeDif { get; set; }

        /// <summary>
        /// 上次零点样时间
        ///</summary>
        [SugarColumn(ColumnName = "time1", ColumnDescription = "上次零点样时间")]
        [Description("上次零点样时间")]
        public DateTime Time1 { get; set; }

        /// <summary>
        /// 本次零点样时间
        ///</summary>
        [SugarColumn(ColumnName = "time2", ColumnDescription = "本次零点样时间")]
        [Description("本次零点样时间")]
        public DateTime Time2 { get; set; }

        #endregion

        #region 公共方法

        /// <summary>
        /// 从设备数据库生成数据
        /// </summary>
        public static List<ZeroCheckData> InitFromDeviceDB(SqlSugarClient db, string snCode)
        {
            try
            {
                var dataList = new List<ZeroCheckData>();

                List<RawZeroCheckData> rawZeroCheckDataList = db.Queryable<RawZeroCheckData>().ToList();
                foreach(var rawZeroCheckData in rawZeroCheckDataList)
                {
                    try
                    {
                        ZeroCheckData zeroCheckData = new ZeroCheckData();
                        zeroCheckData.SNCode = snCode;
                        zeroCheckData.Time = DateTime.Parse(rawZeroCheckData.Time);
                        zeroCheckData.Value = double.Parse(rawZeroCheckData.Value);
                        zeroCheckData.Standard = double.Parse(rawZeroCheckData.Standard);
                        zeroCheckData.Err = double.Parse(rawZeroCheckData.Err);
                        zeroCheckData.ErrDif = double.Parse(rawZeroCheckData.ErrDif);
                        zeroCheckData.JudgeDif = rawZeroCheckData.JudgeDif;
                        zeroCheckData.Judge = rawZeroCheckData.Judge;
                        zeroCheckData.Time1 = DateTime.Parse(rawZeroCheckData.Time1);
                        zeroCheckData.Time2 = DateTime.Parse(rawZeroCheckData.Time2);

                        dataList.Add(zeroCheckData);
                    }
                    catch
                    {
                    }
                }

                return dataList;
            }
            catch(Exception ex)
            {
                throw new Exception($"从数据库提取零点核查数据出错：{ex.Message}");
            }
        }

        #endregion
    }
}