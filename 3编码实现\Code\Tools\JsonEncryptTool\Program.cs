﻿using System;
using System.IO;
using System.Windows.Forms;

namespace JsonEncryptTool
{
    internal static class Program
    {
        [STAThread]
        private static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            var directoryInfo = new DirectoryInfo(Application.StartupPath);
            FileInfo[] files = directoryInfo.GetFiles("*.json");
            foreach(FileInfo fileInfo in files)
            {
                try
                {
                    EncrptFile(fileInfo.FullName);
                }
                catch(Exception ex)
                {
                    // ignored
                }
            }
            MessageBox.Show(@"加密完成!", @"提示");
        }

        private static void EncrptFile(string fileName)
        {
            // 读取文件内容
            string fileContent = File.ReadAllText(fileName);
            // 加密
            fileContent = AesCryptHelper.Encrypt(fileContent);
            // 覆写
            if(File.Exists(fileName))
            {
                File.SetAttributes(fileName, FileAttributes.Normal);
                File.Delete(fileName);
            }
            using(TextWriter textWriter = File.CreateText(fileName))
            {
                textWriter.Write(fileContent);
                textWriter.Flush();
            }
        }
    }
}