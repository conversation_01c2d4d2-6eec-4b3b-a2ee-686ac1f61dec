﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35818.85
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Shell", "Shell\Shell.csproj", "{33812915-0DF5-4F6C-A55B-9F1185734B4F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tools", "Tools", "{A55DA103-6E5C-4F5E-8558-4B1652163C97}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Business", "Business", "{8E0DCE4B-8418-4ECA-BD2D-0CB34388B036}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JsonDecryptTool", "Tools\JsonDecryptTool\JsonDecryptTool.csproj", "{9FD1260B-AE9B-40BC-A1F1-788B3DC92216}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JsonEncryptTool", "Tools\JsonEncryptTool\JsonEncryptTool.csproj", "{6E920032-6899-4B68-893C-5F8D15327D8B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LibBaseModules", "Business\LibBaseModules\LibBaseModules.csproj", "{A510D96B-71BB-42B8-B2DF-BB3BD8D89A72}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LibBusinessModules", "Business\LibBusinessModules\LibBusinessModules.csproj", "{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{33812915-0DF5-4F6C-A55B-9F1185734B4F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{33812915-0DF5-4F6C-A55B-9F1185734B4F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{33812915-0DF5-4F6C-A55B-9F1185734B4F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{33812915-0DF5-4F6C-A55B-9F1185734B4F}.Release|Any CPU.Build.0 = Release|Any CPU
		{9FD1260B-AE9B-40BC-A1F1-788B3DC92216}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9FD1260B-AE9B-40BC-A1F1-788B3DC92216}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9FD1260B-AE9B-40BC-A1F1-788B3DC92216}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9FD1260B-AE9B-40BC-A1F1-788B3DC92216}.Release|Any CPU.Build.0 = Release|Any CPU
		{6E920032-6899-4B68-893C-5F8D15327D8B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6E920032-6899-4B68-893C-5F8D15327D8B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6E920032-6899-4B68-893C-5F8D15327D8B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6E920032-6899-4B68-893C-5F8D15327D8B}.Release|Any CPU.Build.0 = Release|Any CPU
		{A510D96B-71BB-42B8-B2DF-BB3BD8D89A72}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A510D96B-71BB-42B8-B2DF-BB3BD8D89A72}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A510D96B-71BB-42B8-B2DF-BB3BD8D89A72}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A510D96B-71BB-42B8-B2DF-BB3BD8D89A72}.Release|Any CPU.Build.0 = Release|Any CPU
		{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{9FD1260B-AE9B-40BC-A1F1-788B3DC92216} = {A55DA103-6E5C-4F5E-8558-4B1652163C97}
		{6E920032-6899-4B68-893C-5F8D15327D8B} = {A55DA103-6E5C-4F5E-8558-4B1652163C97}
		{A510D96B-71BB-42B8-B2DF-BB3BD8D89A72} = {8E0DCE4B-8418-4ECA-BD2D-0CB34388B036}
		{C31FDB8D-27DB-4088-B49D-8A27AF53CF63} = {8E0DCE4B-8418-4ECA-BD2D-0CB34388B036}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {0EAFC1A2-7105-4269-89AB-93E12B6CF943}
	EndGlobalSection
EndGlobal
