﻿using Fpi.Util;
using LibBusinessModules.Config;
using LibBusinessModules.DB;
using SqlSugar;
using Sunny.UI;
using System;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace LibBusinessModules.UI.DataExtract
{
    /// <summary>
    /// 数据提取
    /// </summary>
    public partial class UC_DataExtract : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 定义SqlSugar客户端
        /// </summary>
        private OneDeviceAllInfo _oneDeviceAllInfo;

        #endregion

        #region 构造

        public UC_DataExtract()
        {
            InitializeComponent();
            txtIP.Text = SystemConfig.GetInstance().FTPInfo.ServerIP;
            txtPort.Text = SystemConfig.GetInstance().FTPInfo.ServerPort.ToString();
            txtUserName.Text = SystemConfig.GetInstance().FTPInfo.UserName;
            txtPassword.Text = SystemConfig.GetInstance().FTPInfo.Password;
            txtFtpPath.Text = SystemConfig.GetInstance().FTPInfo.FilePath;
        }

        #endregion

        #region 事件

        /// <summary>
        /// 选择数据源
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void rdbFtpData_CheckedChanged(object sender, EventArgs e)
        {
            gbFtpData.Enabled = rdbFtpData.Checked;
            gbLoaclData.Enabled = !rdbFtpData.Checked;
        }

        /// <summary>
        /// 选择文件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnFileSelect_Click(object sender, EventArgs e)
        {
            // 创建OpenFileDialog实例
            OpenFileDialog openFileDialog = new OpenFileDialog();
            // 设置标题
            openFileDialog.Title = "选择SQLite数据库文件";
            // 设置过滤器，只显示.db文件
            openFileDialog.Filter = "SQLite数据库文件 (*.db;*.sqlite;*.db3)|*.db;*.sqlite;*.db3";
            // 设置默认目录（可选）
            openFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);

            // 显示对话框并获取结果
            if(openFileDialog.ShowDialog() == DialogResult.OK)
            {
                // 获取所选文件的完整路径
                string selectedFilePath = openFileDialog.FileName;

                // 显示所选文件的路径（例如在文本框中）
                txtFilePath.Text = selectedFilePath;
            }
        }

        /// <summary>
        /// 数据提取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDataExtract_Click(object sender, EventArgs e)
        {
            try
            {
                // FTP数据提取
                if(rdbFtpData.Checked)
                {
                    if(string.IsNullOrEmpty(txtIP.Text))
                    {
                        throw new Exception($"FTP服务器IP不可为空！");
                    }
                    if(string.IsNullOrEmpty(txtPort.Text))
                    {
                        throw new Exception($"FTP服务器端口不可为空！");
                    }
                    if(string.IsNullOrEmpty(txtFtpPath.Text))
                    {
                        throw new Exception($"FTP文件路径不可为空！");
                    }

                    // 提取远程文件名
                    string fileName = Path.GetFileName(txtFtpPath.Text);
                    // 获取临时文件路径并附加文件名
                    string tempFilePath = Path.Combine(Path.GetTempPath(), "IAutoTestX", fileName);
                    if(File.Exists(tempFilePath))
                    {
                        File.Delete(tempFilePath);
                    }
                    // 下载FTP文件到本地
                    FTPHelper.DownloadFileFromFtp(txtIP.Text, txtPort.Text, txtUserName.Text, txtPassword.Text, txtFtpPath.Text, tempFilePath);

                    // 检查文件是否存在
                    if(!File.Exists(tempFilePath))
                    {
                        throw new Exception("FTP提取文件失败！");
                    }

                    // 初始化SqlSugar连接
                    SqlSugarClient db = DBHelper.GetSqliteDBContext($"Data Source={tempFilePath}");

                    _oneDeviceAllInfo = new OneDeviceAllInfo();

                    _oneDeviceAllInfo.InitFromDeviceDB(db);

                    // 删除本地临时文件
                    File.Delete(tempFilePath);
                }
                // 本地数据提取
                else
                {
                    if(!File.Exists(txtFilePath.Text))
                    {
                        throw new Exception("本地文件不存在！");
                    }

                    // 初始化SqlSugar连接
                    SqlSugarClient db = DBHelper.GetSqliteDBContext($"Data Source={txtFilePath.Text}");

                    _oneDeviceAllInfo = new OneDeviceAllInfo();

                    _oneDeviceAllInfo.InitFromDeviceDB(db);
                }

                StringBuilder sb = new StringBuilder();

                txtResult.Clear();
                txtResult.AppendText(_oneDeviceAllInfo.ToString());

                UIMessageBox.ShowInfo("数据提取完成！");

            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"数据提取出错：{ex.Message}");
            }
        }

        private void btnInsert_Click(object sender, EventArgs e)
        {
            try
            {
                if(_oneDeviceAllInfo == null)
                {
                    throw new Exception("请完成数据提取后再执行插入操作！");
                }

                _oneDeviceAllInfo.SaveToPCDB();

                UIMessageBox.ShowInfo("数据插入完成！");
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"数据插入出错：{ex.Message}");
            }
        }

        #endregion
    }
}