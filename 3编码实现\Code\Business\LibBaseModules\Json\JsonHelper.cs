﻿using LibBaseModules.Helper;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Windows.Forms;

namespace LibBaseModules.Json
{
    /// <summary>
    /// JSON帮助类
    /// </summary>
    public static class JsonHelper
    {
        /// <summary>
        /// Json文件存储路径
        /// </summary>
        private static string JsonPath = Path.Combine(Application.StartupPath, "Data");

        /// <summary>
        /// Json序列化设置
        /// </summary>
        private static readonly JsonSerializerSettings settings = new JsonSerializerSettings
        {
            NullValueHandling = NullValueHandling.Include,
            Formatting = Formatting.Indented,
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            TypeNameHandling = TypeNameHandling.Auto
        };

        /// <summary>
        /// 将Model转换成JOSN字符串
        /// </summary>
        /// <param name="value">Model对象</param>
        /// <returns></returns>
        public static string ModelToJson(object value)
        {
            string strRet = "";
            try
            {
                strRet = JsonConvert.SerializeObject(value, settings);
            }
            catch
            {
                strRet = "";
            }
            return strRet;
        }

        /// <summary>
        /// 将String字符串转换成Model
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="value"></param>
        /// <returns></returns>
        public static T JsonToModel<T>(string value)
        {
            JsonSerializerSettings setting = new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore
            };
            return JsonConvert.DeserializeObject<T>(value, setting);
        }

        #region 存储、提取

        private static readonly object syncObj = new object();

        /// <summary>
        /// 数据保存到磁盘中
        /// </summary>
        /// <param name="value">Model对象</param>
        /// <returns></returns>
        public static bool SaveDataToDisk(object value)
        {
            var fileName = Path.Combine(JsonPath, value.GetType().Name + ".json");

            try
            {
                lock(syncObj)
                {
                    //为了断电等原因导致xml文件保存出错，文件损坏，采用先写副本再替换的方式。
                    string tempFileName = fileName.Replace(".json", "_tempback.json");

                    // 判断路径是否存在，不存在则新建
                    string filePath = Path.GetDirectoryName(tempFileName);
                    if(!Directory.Exists(filePath))
                    {
                        Directory.CreateDirectory(filePath);
                    }

                    // 输出到临时文件中
                    using(TextWriter textWriter = File.CreateText(tempFileName))
                    {
                        var fileContent = JsonConvert.SerializeObject(value, settings);
                        textWriter.Write(AesCryptHelper.Encrypt(fileContent));
                        textWriter.Flush();
                    }

                    // 监测原文件是否存在，存在则删除
                    if(File.Exists(fileName))
                    {
                        FileInfo fi = new FileInfo(fileName);
                        if(fi.IsReadOnly)
                        {
                            fi.IsReadOnly = false;
                        }
                        File.Delete(fileName);
                    }

                    // 移动临时文件到正式文件
                    File.Move(tempFileName, fileName);
                }
            }
            catch(Exception ex)
            {
                LogUtil.GetInstance().LogWrite($"序列化存储文件出错。文件名:[{fileName}],错误原因[{ex.Message}]", MsgLevel.Error);
                return false;
            }
            return true;
        }

        /// <summary>
        /// 从磁盘中提取序列化的数据，赋值给传入对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="value"></param>
        /// <returns></returns>
        public static void LoadDataFromDisk(object value)
        {
            string typeName = value.GetType().Name;
            try
            {
                JsonConvert.PopulateObject(LoadJsonStrFromDisk(typeName), value, settings);
            }
            catch(Exception ex)
            {
                LogUtil.GetInstance().LogWrite($"反序列化加载文件出错。文件类型:[{typeName}],错误原因[{ex.Message}]", MsgLevel.Error);
            }
        }

        /// <summary>
        /// 从磁盘中提取序列化的数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="value"></param>
        /// <returns></returns>
        public static T LoadDataFromDisk<T>()
        {
            T result = default;
            string typeName = typeof(T).Name;
            try
            {
                result = JsonToModel<T>(LoadJsonStrFromDisk(typeName));
            }
            catch(Exception ex)
            {
                LogUtil.GetInstance().LogWrite($"反序列化加载文件出错。文件类型:[{typeName}],错误原因[{ex.Message}]", MsgLevel.Error);
            }

            return result;
        }

        /// <summary>
        /// 从磁盘中提取序列化的数据字符串
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string LoadJsonStrFromDisk(string typeName)
        {
            lock(syncObj)
            {
                string result = string.Empty;

                var fileName = Path.Combine(JsonPath, typeName + ".json");

                if(File.Exists(fileName))
                {
                    // 读取文件内容
                    string fileContent = File.ReadAllText(fileName);
                    try
                    {
                        // 解密
                        fileContent = AesCryptHelper.Decrypt(fileContent);
                    }
                    catch(Exception e)
                    {
                        // 解密失败时，使用原文件内容
                    }
                    result = fileContent;
                }

                return result;
            }
        }

        #endregion
    }
}