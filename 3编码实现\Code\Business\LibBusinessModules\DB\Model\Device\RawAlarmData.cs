﻿using SqlSugar;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.Device
{
    /// <summary>
    /// 报警记录表
    ///</summary>
    [SugarTable("alarmData")]
    [Description("报警记录表")]
    public class RawAlarmData
    {
        /// <summary>
        /// 时间
        ///</summary>
        [SugarColumn(ColumnName = "time")]
        [Description("时间")]
        public string? Time { get; set; }

        /// <summary>
        /// 报警码
        ///</summary>
        [SugarColumn(ColumnName = "code")]
        [Description("报警码")]
        public string? Code { get; set; }

        /// <summary>
        /// 报警详情
        ///</summary>
        [SugarColumn(ColumnName = "msg")]
        [Description("报警详情")]
        public string? Msg { get; set; }

        /// <summary>
        /// 建议操作
        ///</summary>
        [SugarColumn(ColumnName = "tip")]
        [Description("建议操作")]
        public string? Tip { get; set; }
    }
}