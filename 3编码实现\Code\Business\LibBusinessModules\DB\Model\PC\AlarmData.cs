﻿using LibBusinessModules.DB.Models.Device;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.PC
{
    /// <summary>
    /// 报警记录
    ///</summary>
    [SugarTable("alarm_data")]
    [SugarIndex("index_sncode_time", nameof(SNCode), OrderByType.Asc, nameof(Time), OrderByType.Asc)]
    [Description("报警记录")]
    public class AlarmData
    {
        #region 字段属性

        /// <summary>
        /// 设备序列号
        ///</summary>
        [SugarColumn(ColumnName = "sncode", ColumnDescription = "设备序列号")]
        [Description("设备序列号")]
        public string SNCode { get; set; }

        /// <summary>
        /// 数据时间
        ///</summary>
        [SugarColumn(ColumnName = "time", SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "数据时间")]
        [Description("数据时间")]
        public DateTime Time { get; set; }

        /// <summary>
        /// 报警码
        ///</summary>
        [SugarColumn(ColumnName = "code", ColumnDescription = "报警码")]
        [Description("报警码")]
        public string Code { get; set; }

        /// <summary>
        /// 报警详情
        ///</summary>
        [SugarColumn(ColumnName = "msg", ColumnDescription = "报警详情")]
        [Description("报警详情")]
        public string Msg { get; set; }

        /// <summary>
        /// 建议操作
        ///</summary>
        [SugarColumn(ColumnName = "tip", ColumnDescription = "建议操作")]
        [Description("建议操作")]
        public string Tip { get; set; }

        #endregion

        #region 公共方法

        /// <summary>
        /// 从设备数据库生成数据
        /// </summary>
        public static List<AlarmData> InitFromDeviceDB(SqlSugarClient db, string snCode)
        {
            try
            {
                List<AlarmData> dataList = new List<AlarmData>();

                List<RawAlarmData> alarmDataList = db.Queryable<RawAlarmData>().ToList();
                foreach(RawAlarmData rawAlarmData in alarmDataList)
                {
                    try
                    {
                        AlarmData operData = new AlarmData();
                        operData.SNCode = snCode;
                        operData.Time = DateTime.Parse(rawAlarmData.Time);
                        operData.Code = rawAlarmData.Code;
                        operData.Msg = rawAlarmData.Msg;
                        operData.Tip = rawAlarmData.Tip;

                        dataList.Add(operData);
                    }
                    catch
                    {
                    }
                }

                return dataList;
            }
            catch(Exception ex)
            {
                throw new Exception($"从数据库提取报警记录出错：{ex.Message}");
            }
        }

        #endregion
    }
}